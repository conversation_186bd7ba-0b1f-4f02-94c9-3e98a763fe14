#
# RS485 Filter Driver Build Configuration
# Windows Driver Kit (WDK) Build Environment
#

TARGETNAME=RS485FilterDriver
TARGETTYPE=DYNLINK
DRIVERTYPE=WDF

# Target platform and architecture
_NT_TARGET_VERSION=$(_NT_TARGET_VERSION_WIN10)
KMDF_VERSION_MAJOR=1
UMDF_VERSION_MAJOR=2

# Use UMDF 2.0
USE_MSVCRT=1
UMDF_VERSION=2.0

# Driver entry point
DLLENTRY=_DllMainCRTStartup

# Source files
SOURCES=\
    RS485FilterDriver.cpp \
    RS485Queue.cpp \
    RS485Buffer.cpp \
    RS485Protocol.cpp \
    RS485Device.cpp

# Include directories
INCLUDES=\
    ..\Include; \
    $(DDK_INC_PATH); \
    $(SDK_INC_PATH)

# Target libraries
TARGETLIBS=\
    $(SDK_LIB_PATH)\kernel32.lib \
    $(SDK_LIB_PATH)\user32.lib \
    $(SDK_LIB_PATH)\advapi32.lib \
    $(SDK_LIB_PATH)\setupapi.lib \
    $(DDK_LIB_PATH)\wdf01000.lib \
    $(DDK_LIB_PATH)\wdfplatform.lib

# Compiler definitions
C_DEFINES=$(C_DEFINES) -DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN

# Warning level
MSC_WARNING_LEVEL=/W4 /WX

# Debug settings
!if "$(DDKBUILDENV)" == "chk"
C_DEFINES=$(C_DEFINES) -DDBG=1
!endif

# Precompiled headers (optional)
# PRECOMPILED_INCLUDE=RS485FilterDriver.h
# PRECOMPILED_PCH=precomp.pch
# PRECOMPILED_OBJ=precomp.obj

# Module definition file
DLLDEF=RS485FilterDriver.def

# Version information
RCNOFONTMAP=1

# Linker options
LINKER_FLAGS=$(LINKER_FLAGS) /SUBSYSTEM:NATIVE

# WDF specific settings
WDF_KMDF_VERSION=1.15
WDF_UMDF_VERSION=2.0

# Code analysis
RUN_WPP=

# Additional compiler flags
USER_C_FLAGS=/analyze

# Export definitions
DLLDEF=RS485FilterDriver.def
