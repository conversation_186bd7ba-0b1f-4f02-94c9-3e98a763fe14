@echo off
REM ===================================================================
REM RS485 Driver Build and Package Script
REM Builds driver, creates catalog, and packages installer
REM ===================================================================

setlocal enabledelayedexpansion

echo ===================================================================
echo RS485 Driver Build and Package Script
echo ===================================================================

REM Set paths
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set DRIVER_DIR=%PROJECT_ROOT%\Driver
set INSTALLER_DIR=%PROJECT_ROOT%\Installer
set BUILD_DIR=%PROJECT_ROOT%\Build
set PACKAGE_DIR=%PROJECT_ROOT%\Package

REM Create directories
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%PACKAGE_DIR%" mkdir "%PACKAGE_DIR%"

echo.
echo Step 1: Building RS485 Filter Driver...
echo ===================================================================

REM Build the driver using MSBuild
call "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" ^
    "%DRIVER_DIR%\RS485FilterDriver.vcxproj" ^
    /p:Configuration=Release ^
    /p:Platform=x64 ^
    /v:minimal

if errorlevel 1 (
    echo ERROR: Driver build failed!
    pause
    exit /b 1
)

echo Driver build completed successfully.

echo.
echo Step 2: Creating Driver Catalog...
echo ===================================================================

REM Create catalog file for driver signing
cd /d "%DRIVER_DIR%"

REM Use Inf2Cat to create catalog
call "C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\Inf2Cat.exe" ^
    /driver:. ^
    /os:10_X64

if errorlevel 1 (
    echo WARNING: Catalog creation failed. Driver may not be signed.
) else (
    echo Catalog created successfully.
)

echo.
echo Step 3: Copying FTDI Driver...
echo ===================================================================

REM Copy FTDI driver to installer directory
if exist "%PROJECT_ROOT%\CDM2123620_Setup.exe" (
    copy "%PROJECT_ROOT%\CDM2123620_Setup.exe" "%INSTALLER_DIR%\"
    echo FTDI driver copied successfully.
) else (
    echo WARNING: FTDI driver not found! Please place CDM2123620_Setup.exe in project root.
    echo You can download it from: https://ftdichip.com/drivers/vcp-drivers/
)

echo.
echo Step 4: Building Installer...
echo ===================================================================

REM Build the installer
call "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" ^
    "%INSTALLER_DIR%\RS485DriverInstaller.vcxproj" ^
    /p:Configuration=Release ^
    /p:Platform=x64 ^
    /v:minimal

if errorlevel 1 (
    echo ERROR: Installer build failed!
    pause
    exit /b 1
)

echo Installer build completed successfully.

echo.
echo Step 5: Creating Final Package...
echo ===================================================================

REM Copy final installer to package directory
copy "%BUILD_DIR%\Release\x64\RS485DriverInstaller.exe" "%PACKAGE_DIR%\"

REM Create package info
echo RS485 Driver Installation Package > "%PACKAGE_DIR%\README.txt"
echo ================================== >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo This package contains: >> "%PACKAGE_DIR%\README.txt"
echo 1. FTDI VCP Driver (CDM2123620) >> "%PACKAGE_DIR%\README.txt"
echo 2. RS485 Protocol Filter Driver >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo Installation Instructions: >> "%PACKAGE_DIR%\README.txt"
echo 1. Right-click RS485DriverInstaller.exe >> "%PACKAGE_DIR%\README.txt"
echo 2. Select "Run as administrator" >> "%PACKAGE_DIR%\README.txt"
echo 3. Follow the installation prompts >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo Build Date: %DATE% %TIME% >> "%PACKAGE_DIR%\README.txt"

echo.
echo ===================================================================
echo BUILD COMPLETE!
echo ===================================================================
echo.
echo Final installer created: %PACKAGE_DIR%\RS485DriverInstaller.exe
echo.
echo To install the driver:
echo 1. Right-click RS485DriverInstaller.exe
echo 2. Select "Run as administrator"
echo 3. Follow the installation prompts
echo.
echo The installer will automatically:
echo - Install FTDI VCP Driver
echo - Install RS485 Filter Driver
echo - Configure all necessary settings
echo.

pause
