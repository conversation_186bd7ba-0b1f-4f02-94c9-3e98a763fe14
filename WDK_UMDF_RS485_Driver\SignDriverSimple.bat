@echo off
REM ===================================================================
REM Simple RS485 Driver Signing Script
REM ===================================================================

echo ===================================================================
echo Simple RS485 Driver Signing Tool
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Enabling test signing mode...
echo ===================================================================

REM Enable test signing
bcdedit /set testsigning on
if %errorLevel% equ 0 (
    echo Test signing enabled successfully.
    echo IMPORTANT: You must REBOOT your computer for changes to take effect!
) else (
    echo WARNING: Failed to enable test signing. You may need to disable Secure Boot.
)

echo.
echo Step 2: Creating test certificate...
echo ===================================================================

REM Create test certificate using PowerShell (more reliable)
powershell -Command "try { $cert = New-SelfSignedCertificate -Subject 'CN=RS485TestCert' -CertStoreLocation 'Cert:\CurrentUser\My' -KeyUsage DigitalSignature -Type CodeSigningCert; Write-Host 'Certificate created successfully'; Export-Certificate -Cert $cert -FilePath 'RS485TestCert.cer' } catch { Write-Host 'Certificate creation failed:' $_.Exception.Message }"

echo.
echo Step 3: Installing certificate...
echo ===================================================================

REM Install certificate to trusted stores
if exist "RS485TestCert.cer" (
    certlm -add "RS485TestCert.cer" -s -r localMachine root
    certlm -add "RS485TestCert.cer" -s -r localMachine trustedpublisher
    echo Certificate installed to trusted stores.
) else (
    echo WARNING: Certificate file not found. Manual certificate installation may be required.
)

echo.
echo Step 4: Creating catalog file...
echo ===================================================================

REM Try to create catalog file
if exist "Driver\RS485FilterDriver.inf" (
    pushd Driver
    inf2cat /driver:. /os:10_X64
    if %errorLevel% equ 0 (
        echo Catalog file created successfully.
    ) else (
        echo WARNING: Failed to create catalog file. This is normal if inf2cat is not available.
    )
    popd
) else (
    echo WARNING: INF file not found in Driver directory.
)

echo.
echo Step 5: Signing files...
echo ===================================================================

REM Sign the driver DLL if it exists
if exist "Driver\Build\Release\x64\RS485FilterDriver.dll" (
    signtool sign /v /s My /n "RS485TestCert" /fd SHA256 "Driver\Build\Release\x64\RS485FilterDriver.dll"
    if %errorLevel% equ 0 (
        echo Driver DLL signed successfully.
    ) else (
        echo WARNING: Failed to sign driver DLL.
    )
) else (
    echo WARNING: Driver DLL not found. Please build the driver first.
)

REM Sign the catalog file if it exists
if exist "Driver\RS485FilterDriver.cat" (
    signtool sign /v /s My /n "RS485TestCert" /fd SHA256 "Driver\RS485FilterDriver.cat"
    if %errorLevel% equ 0 (
        echo Catalog file signed successfully.
    ) else (
        echo WARNING: Failed to sign catalog file.
    )
)

echo.
echo ===================================================================
echo Signing Process Complete
echo ===================================================================
echo.
echo Summary:
echo - Test signing mode: ENABLED (requires reboot)
echo - Test certificate: Created and installed
echo - Driver files: Signed (if available)
echo.
echo Next steps:
echo 1. REBOOT your computer to activate test signing mode
echo 2. After reboot, run: .\FinalOutput\RS485DriverInstaller.exe
echo.
echo NOTE: This is for DEVELOPMENT ONLY!
echo For production, use a proper code signing certificate.
echo.

pause
