#ifndef RS485_INTERFACE_PROTOCOL_H
#define RS485_INTERFACE_PROTOCOL_H

//
// RS485 Protocol Structures and Definitions for Interface
// ZES Protocol Implementation for User-Mode Interface
//

#include "RS485InterfaceCommon.h"

//
// ZES Protocol Frame Structure (16 bytes total)
//
#pragma pack(push, 1)
typedef struct _RS485_FRAME {
    UINT8   Header;         // 0xAA - Frame start marker
    UINT8   Id;             // Function code and slave address
    UINT8   Payload[12];    // 12-byte payload (4-byte key + 8-byte data)
    UINT8   Crc8;           // CRC-8 checksum
    UINT8   Trailer;        // 0x0D - Frame end marker
} RS485_FRAME, *PRS485_FRAME;
#pragma pack(pop)

//
// Payload Structure (12 bytes)
//
#pragma pack(push, 1)
typedef struct _RS485_PAYLOAD {
    UINT8   Key[4];         // 4-byte ASCII command key (e.g., "S001", "U005")
    UINT8   Data[8];        // 8-byte data content
} RS485_PAYLOAD, *PRS485_PAYLOAD;
#pragma pack(pop)

//
// Frame State Enumeration
//
typedef enum _RS485_FRAME_STATE {
    FrameStateWaitingHeader = 0,    // Looking for 0xAA
    FrameStateReadingId,            // Reading ID byte (contains function code)
    FrameStateReadingPayload,       // Reading 12-byte payload
    FrameStateReadingCrc,           // Reading CRC8
    FrameStateReadingTrailer,       // Looking for 0x0D
    FrameStateComplete,             // Frame ready for processing
    FrameStateError                 // CRC or format error
} RS485_FRAME_STATE, *PRS485_FRAME_STATE;

//
// Function Code Definitions
//
#define RS485_FUNCTION_SYSTEM_CONFIG    0x53    // 'S' - System configuration commands
#define RS485_FUNCTION_USER_CONFIG      0x55    // 'U' - User configuration commands
#define RS485_FUNCTION_DATA_QUERY       0x41    // 'A' - Data query commands
#define RS485_FUNCTION_MODEL_DATA       0x57    // 'W' - Model data commands
#define RS485_FUNCTION_ASSIGN_DATA      0x01    // Data assignment function
#define RS485_BROADCAST_ADDRESS         0xFF    // Broadcast address for all devices

//
// System Configuration Commands (S-series)
//
#define RS485_CMD_SLAVE_ADDRESS         "S001"  // Set/Get slave address (1-247)
#define RS485_CMD_BAUD_RATE            "S002"  // Set/Get baud rate
#define RS485_CMD_SYSTEM_RESET         "S003"  // System reset command
#define RS485_CMD_FIRMWARE_VERSION     "S004"  // Get firmware version
#define RS485_CMD_HARDWARE_VERSION     "S005"  // Get hardware version

//
// User Configuration Commands (U-series)
//
#define RS485_CMD_MEASUREMENT_INTERVAL  "U001"  // Set measurement interval (ms)
#define RS485_CMD_FILTER_COEFFICIENT   "U002"  // Set digital filter coefficient
#define RS485_CMD_CALIBRATION_OFFSET   "U003"  // Set calibration offset
#define RS485_CMD_CALIBRATION_SCALE    "U004"  // Set calibration scale factor
#define RS485_CMD_ALARM_THRESHOLD      "U005"  // Set alarm threshold

//
// Data Query Commands (A-series)
//
#define RS485_CMD_CURRENT_VALUE        "A001"  // Get current measurement value
#define RS485_CMD_PEAK_VALUE           "A002"  // Get peak value since last reset
#define RS485_CMD_AVERAGE_VALUE        "A003"  // Get average value
#define RS485_CMD_STATUS_FLAGS         "A004"  // Get device status flags
#define RS485_CMD_ERROR_COUNT          "A005"  // Get error count

//
// Model Data Commands (W-series)
//
#define RS485_CMD_MODEL_NUMBER         "W001"  // Get model number
#define RS485_CMD_SERIAL_NUMBER        "W002"  // Get serial number
#define RS485_CMD_MANUFACTURE_DATE     "W003"  // Get manufacture date
#define RS485_CMD_CALIBRATION_DATE     "W004"  // Get last calibration date
#define RS485_CMD_DEVICE_CAPABILITIES  "W005"  // Get device capabilities

//
// Frame Processing Result
//
typedef enum _RS485_FRAME_PROCESS_RESULT {
    FrameProcessContinue = 0,       // Continue processing, need more data
    FrameProcessComplete,           // Frame processing complete
    FrameProcessFrameError,         // Frame format error
    FrameProcessCrcError,           // CRC validation failed
    FrameProcessTimeout             // Processing timeout
} RS485_FRAME_PROCESS_RESULT;

//
// IOCTL Input Structures
//

// System Configuration Input
#pragma pack(push, 1)
typedef struct _RS485_SYSTEM_CONFIG_INPUT {
    CHAR    CommandKey[5];      // 4-byte command key + null terminator (e.g., "S001")
    UINT8   FunctionCode;       // Function code for frame
    UINT8   DeviceAddress;      // Target device address
    UINT32  Value;              // Configuration value
    UINT8   Reserved[3];        // Padding for alignment
} RS485_SYSTEM_CONFIG_INPUT, *PRS485_SYSTEM_CONFIG_INPUT;
#pragma pack(pop)

// User Configuration Input
#pragma pack(push, 1)
typedef struct _RS485_USER_CONFIG_INPUT {
    CHAR    CommandKey[5];      // 4-byte command key + null terminator (e.g., "U001")
    UINT8   FunctionCode;       // Function code for frame
    UINT8   DeviceAddress;      // Target device address
    UINT32  Value;              // Configuration value
    UINT8   Reserved[3];        // Padding for alignment
} RS485_USER_CONFIG_INPUT, *PRS485_USER_CONFIG_INPUT;
#pragma pack(pop)

// Data Request Input
#pragma pack(push, 1)
typedef struct _RS485_DATA_REQUEST_INPUT {
    CHAR    CommandKey[5];      // 4-byte command key + null terminator (e.g., "A001")
    UINT8   FunctionCode;       // Function code for frame
    UINT8   DeviceAddress;      // Target device address
    UINT32  TimeoutMs;          // Request timeout in milliseconds
    UINT8   Reserved[3];        // Padding for alignment
} RS485_DATA_REQUEST_INPUT, *PRS485_DATA_REQUEST_INPUT;
#pragma pack(pop)

// Response Receive Input
#pragma pack(push, 1)
typedef struct _RS485_RESPONSE_RECEIVE_INPUT {
    UINT8   ExpectedFrameId;    // Expected frame ID
    UINT32  TimeoutMs;          // Receive timeout in milliseconds
    UINT32  MaxDataSize;        // Maximum data size to receive
    UINT8   Reserved[3];        // Padding for alignment
} RS485_RESPONSE_RECEIVE_INPUT, *PRS485_RESPONSE_RECEIVE_INPUT;
#pragma pack(pop)

//
// CRC-8 Calculation Function
//
UINT8 RS485CalculateCrc8(_In_reads_(Length) const UINT8* Data, _In_ SIZE_T Length);

//
// Frame Validation Functions
//
BOOLEAN RS485ValidateFrame(_In_ const RS485_FRAME* Frame);
BOOLEAN RS485ValidatePayload(_In_ const RS485_PAYLOAD* Payload);

//
// Payload Helper Functions
//
VOID RS485InitializePayload(_Out_ PRS485_PAYLOAD Payload, _In_reads_(4) const CHAR* Key);
VOID RS485StorePayloadInteger(_Out_ PRS485_PAYLOAD Payload, _In_ UINT32 Value);
VOID RS485StorePayloadFloat(_Out_ PRS485_PAYLOAD Payload, _In_ FLOAT Value);
VOID RS485StorePayloadString(_Out_ PRS485_PAYLOAD Payload, _In_reads_(8) const CHAR* String);
UINT32 RS485ExtractPayloadInteger(_In_ const RS485_PAYLOAD* Payload);
FLOAT RS485ExtractPayloadFloat(_In_ const RS485_PAYLOAD* Payload);
VOID RS485ExtractPayloadString(_In_ const RS485_PAYLOAD* Payload, _Out_writes_(9) CHAR* String);

#endif // RS485_INTERFACE_PROTOCOL_H
