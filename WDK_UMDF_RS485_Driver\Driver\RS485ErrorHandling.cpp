//
// RS485ErrorHandling.cpp - Error handling and statistics functions
// RS485 UMDF 2.0 Driver for AI-SLDAP Communication
//

#include "RS485FilterDriver.h"

//
// Error string mapping
//
static const char* g_ErrorStrings[] = {
    "Success",                              // RS485_SUCCESS
    "Invalid parameter",                    // RS485_ERROR_INVALID_PARAMETER
    "Device not found",                     // RS485_ERROR_DEVICE_NOT_FOUND
    "Communication failed",                 // RS485_ERROR_COMMUNICATION_FAILED
    "Timeout occurred",                     // RS485_ERROR_TIMEOUT
    "Buffer overflow",                      // RS485_ERROR_BUFFER_OVERFLOW
    "Buffer underflow",                     // RS485_ERROR_BUFFER_UNDERFLOW
    "Invalid data format",                  // RS485_ERROR_INVALID_DATA_FORMAT
    "CRC error",                           // RS485_ERROR_CRC_ERROR
    "Frame error",                         // RS485_ERROR_FRAME_ERROR
    "Hardware error",                      // RS485_ERROR_HARDWARE_ERROR
    "Driver not initialized",              // RS485_ERROR_DRIVER_NOT_INITIALIZED
    "Operation not supported",             // RS485_ERROR_OPERATION_NOT_SUPPORTED
    "Resource unavailable",                // RS485_ERROR_RESOURCE_UNAVAILABLE
    "Access denied",                       // RS485_ERROR_ACCESS_DENIED
    "Unknown error"                        // RS485_ERROR_UNKNOWN
};

//
// Get error string description
//
const char* RS485GetErrorString(RS485_ERROR error)
{
    if (error >= 0 && error < ARRAYSIZE(g_ErrorStrings)) {
        return g_ErrorStrings[error];
    }
    return g_ErrorStrings[RS485_PROTOCOL_ERROR];
}

//
// Initialize error statistics
//
void RS485InitializeErrorStatistics(PRS485_ERROR_STATISTICS statistics)
{
    if (statistics == NULL) {
        return;
    }

    RtlZeroMemory(statistics, sizeof(RS485_ERROR_STATISTICS));
    // Statistics structure initialized to zero
}

//
// Update error statistics
//
void RS485UpdateErrorStatistics(PRS485_ERROR_STATISTICS statistics, RS485_ERROR error)
{
    if (statistics == NULL) {
        return;
    }

    statistics->TotalErrors++;
    // Update error statistics (LastError and LastErrorTime not available in this structure)

    // Update specific error counters
    switch (error) {
        case RS485_TIMEOUT_ERROR:
            statistics->TimeoutErrors++;
            break;
        case RS485_CRC_ERROR:
            statistics->CrcErrors++;
            break;
        case RS485_FRAME_SYNC_ERROR:
            statistics->ProtocolErrors++;
            break;
        case RS485_BUFFER_OVERFLOW:
            statistics->BufferOverflows++;
            break;
        case RS485_PROTOCOL_ERROR:
            statistics->ProtocolErrors++;
            break;
        case RS485_CONNECTION_ERROR:
        case RS485_DEVICE_NOT_FOUND:
        case RS485_INVALID_HANDLE:
            statistics->HardwareErrors++;
            break;
        default:
            // Other errors - just count in total
            break;
    }
}

//
// Handle frame error
//
void RS485HandleFrameError(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame, RS485_ERROR error)
{
    if (deviceContext == NULL || frame == NULL) {
        return;
    }

    RS485_DEBUG_PRINT("Frame error occurred: %s", RS485GetErrorString(error));

    // Update error statistics
    RS485UpdateErrorStatistics(&deviceContext->ErrorStatistics, error);

    // Log frame details for debugging
    RS485_DEBUG_PRINT("Frame details - Header: 0x%02X, ID: 0x%02X, CRC: 0x%04X, Trailer: 0x%02X",
                      frame->Header, frame->SlaveId, frame->Crc, frame->Trailer);

    // Handle specific error types
    switch (error) {
        case RS485_CRC_ERROR:
            // Request retransmission if possible
            // Note: LastCrcError field not available in current structure
            break;

        case RS485_FRAME_SYNC_ERROR:
            // Reset frame synchronization
            // Note: FrameSyncLost field not available in current structure
            break;

        case RS485_TIMEOUT_ERROR:
            // Update timeout statistics
            // Note: LastTimeout field not available in current structure
            break;

        default:
            break;
    }
}

//
// Handle buffer overflow - This function is implemented in RS485Device.cpp
// Removing duplicate implementation to fix linker error
//
