# AI-SLDAP RS485 Driver Installation Guide

## Overview

This package provides a complete RS485 communication solution that integrates:
- **FTDI VCP Driver** (`CDM2123620_Setup.exe`) - Hardware interface driver
- **AI-SLDAP RS485 Filter Driver** - Protocol processing and application interface

The installation creates a single, unified driver package that users can install with one process.

## System Requirements

- **Operating System**: Windows 10/11 (x64 or x86)
- **Privileges**: Administrator rights required
- **Hardware**: FTDI-based RS485 USB adapter
- **Framework**: Windows Driver Framework (WDF) 2.15 or later

## Supported FTDI Devices

The driver supports the following FTDI chip variants:
- **FT232R** (USB VID_0403&PID_6001)
- **FT232H** (USB VID_0403&PID_6015)
- **FT2232D** (USB VID_0403&PID_6010)
- **FT2232H** (USB VID_0403&PID_6011)

## Installation Package Contents

```
Installation/
├── CDM2123620_Setup.exe          # FTDI VCP Driver installer (user must provide)
├── RS485FilterDriver.dll         # Our UMDF driver binary
├── RS485FilterDriver.inf         # Driver installation information
├── InstallDriver.bat             # Batch installation script
├── InstallDriver.ps1             # PowerShell installation script (recommended)
├── UninstallDriver.ps1           # PowerShell uninstallation script
├── INSTALLATION_GUIDE.md         # This guide
├── PreparePackage.ps1            # Package preparation script
└── TestApplication/
    └── SimpleDriverTest.exe      # Test application
```

## Installation Methods

### Method 1: PowerShell Script (Recommended)

1. **Download and extract** the installation package
2. **Copy FTDI driver** - Place `CDM2123620_Setup.exe` in the Installation folder
3. **Run PowerShell as Administrator**:
   ```powershell
   Right-click PowerShell → "Run as administrator"
   ```
4. **Navigate to installation folder**:
   ```powershell
   cd "C:\Path\To\Installation\Folder"
   ```
5. **Run installation script**:
   ```powershell
   .\InstallDriver.ps1
   ```

#### PowerShell Script Options:
- **Silent installation**: `.\InstallDriver.ps1 -Silent`
- **Skip FTDI driver**: `.\InstallDriver.ps1 -SkipFTDI` (if already installed)

### Method 2: Batch Script

1. **Download and extract** the installation package
2. **Copy FTDI driver** - Place `CDM2123620_Setup.exe` in the Installation folder
3. **Right-click** `InstallDriver.bat` → **"Run as administrator"**

### Method 3: Manual Installation

#### Step 1: Install FTDI VCP Driver
```cmd
CDM2123620_Setup.exe /S
```

#### Step 2: Install RS485 Filter Driver
```cmd
pnputil /add-driver RS485FilterDriver.inf /install
```

## Verification

After installation, verify the driver is working:

1. **Check Device Manager**:
   - Connect your RS485 device
   - Look for "AI-SLDAP RS485 Communication Driver" under "Ports (COM & LPT)"

2. **Run Test Application**:
   ```cmd
   cd TestApplication
   SimpleDriverTest.exe
   ```

3. **Check Driver Store**:
   ```cmd
   pnputil /enum-drivers | findstr RS485
   ```

## Troubleshooting

### Common Issues

#### "Access Denied" Error
- **Solution**: Run installation as Administrator
- **Check**: Right-click → "Run as administrator"

#### "Driver Not Found" Error
- **Solution**: Ensure all files are in the same directory
- **Check**: Verify `RS485FilterDriver.dll` and `.inf` files are present

#### "FTDI Driver Installation Failed"
- **Solution**: Install FTDI driver manually first
- **Check**: Download latest FTDI VCP driver from FTDI website

#### Device Not Recognized
- **Solution**: Check hardware connection and device compatibility
- **Check**: Verify device uses supported FTDI chip (see supported devices list)

### Advanced Troubleshooting

#### Enable Driver Verifier (for debugging)
```cmd
verifier /standard /driver RS485FilterDriver.dll
```

#### Check Windows Event Log
```cmd
eventvwr.msc
→ Windows Logs → System
→ Filter by "RS485" or "FTDI"
```

#### Driver Installation Logs
```cmd
# Check PnP installation logs
type %windir%\inf\setupapi.dev.log | findstr RS485
```

## Uninstallation

### Using PowerShell Script (Recommended)
```powershell
.\UninstallDriver.ps1
```

### Remove FTDI Driver Too
```powershell
.\UninstallDriver.ps1 -RemoveFTDI
```

### Manual Uninstallation
```cmd
# Remove our driver
pnputil /delete-driver oem*.inf /uninstall /force

# Remove FTDI driver (optional)
# Use "Add or Remove Programs" to uninstall FTDI VCP drivers
```

## Development and Testing

### Test Application Usage
```cmd
SimpleDriverTest.exe
```

The test application will:
- Detect the RS485 driver
- Test basic IOCTL communication
- Verify buffer status
- Display connection information

### API Integration

Applications can communicate with the driver using:
- **DeviceIoControl** calls with defined IOCTL codes
- **Standard Windows file I/O** operations
- **Interface library** (if using the C++ wrapper)

### IOCTL Codes
```cpp
#define IOCTL_RS485_SEND_FRAME      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_FRAME   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_STATUS      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_RESET_BUFFERS   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_CONFIG      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define IOCTL_RS485_GET_CONFIG      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)
```

## Support and Contact

For technical support or issues:
- Check the troubleshooting section above
- Review Windows Event Logs for error details
- Ensure hardware compatibility with supported FTDI devices
- Verify all installation steps were completed successfully

## Version Information

- **Driver Version**: 1.0.0.0
- **UMDF Version**: 2.15.0
- **Supported Windows**: 10/11 (x64/x86)
- **FTDI VCP Version**: CDM 2.12.36.20 (or compatible)

---

**Note**: This driver integrates with FTDI VCP drivers to provide a complete RS485 communication solution. The FTDI driver handles hardware communication while our filter driver provides protocol processing and application interface.
