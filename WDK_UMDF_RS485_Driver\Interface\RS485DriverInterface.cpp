//
// RS485 Driver Interface Implementation
// High-level API wrapper that abstracts DeviceIoControl calls
//

#include "RS485DriverInterface.h"
#include <setupapi.h>
#include <devguid.h>
#include <iostream>
#include <chrono>
#include <thread>

//
// Internal Implementation Class (PIMPL Pattern)
//
class RS485DriverInterface::Impl {
public:
    HANDLE driverHandle;
    std::string devicePath;
    bool isOpen;

    Impl() : driverHandle(INVALID_HANDLE_VALUE), isOpen(false) {}

    ~Impl() {
        if (isOpen && driverHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(driverHandle);
        }
    }
};

//
// Constructor
//
RS485DriverInterface::RS485DriverInterface()
    : m_pImpl(std::make_unique<Impl>())
    , m_currentSlaveAddress(0)
    , m_bufferOverflowPolicy(RS485_BUFFER_OVERFLOW_TRIGGER_ERROR)
    , m_bufferThresholdPercent(80)
{
    // Initialize with default values
}

//
// Destructor
//
RS485DriverInterface::~RS485DriverInterface() {
    if (isPortOpen()) {
        closePort();
    }
}

//
// Open Port
//
RS485_CONNECTION_RESULT RS485DriverInterface::openPort(const std::string& devicePath) {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (m_pImpl->isOpen) {
        return ConnectionResultPortAlreadyOpen;
    }

    // Open device handle
    m_pImpl->driverHandle = CreateFileA(
        devicePath.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL | FILE_FLAG_OVERLAPPED,
        NULL
    );

    if (m_pImpl->driverHandle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        return mapWindowsErrorToConnectionResult(error);
    }

    m_pImpl->devicePath = devicePath;
    m_pImpl->isOpen = true;

    return ConnectionResultSuccess;
}

//
// Close Port
//
RS485_CONNECTION_RESULT RS485DriverInterface::closePort() {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return ConnectionResultSuccess; // Already closed
    }

    if (m_pImpl->driverHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_pImpl->driverHandle);
        m_pImpl->driverHandle = INVALID_HANDLE_VALUE;
    }

    m_pImpl->isOpen = false;
    m_pImpl->devicePath.clear();

    return ConnectionResultSuccess;
}

//
// Check if Port is Open
//
bool RS485DriverInterface::isPortOpen() const {
    std::lock_guard<std::mutex> lock(m_apiMutex);
    return m_pImpl->isOpen;
}

//
// Get Port Information
//
RS485_PORT_RESULT RS485DriverInterface::getPortInfo(PortInfo& info) {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return PortResultPortNotOpen;
    }

    info.devicePath = m_pImpl->devicePath;
    info.friendlyName = "RS485 Filter Driver";
    info.isOpen = m_pImpl->isOpen;
    info.baudRate = 9600; // Default, would need IOCTL to get actual value
    info.driverVersion = "*******";

    return PortResultSuccess;
}

//
// Enumerate Devices
//
RS485_ENUMERATION_RESULT RS485DriverInterface::enumerateDevices(std::vector<DeviceInfo>& deviceList) {
    deviceList.clear();

    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
        &GUID_DEVINTERFACE_RS485_FILTER,
        NULL,
        NULL,
        DIGCF_PRESENT | DIGCF_DEVICEINTERFACE
    );

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        return EnumerationResultSystemError;
    }

    SP_DEVICE_INTERFACE_DATA deviceInterfaceData;
    deviceInterfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);

    DWORD deviceIndex = 0;
    while (SetupDiEnumDeviceInterfaces(
        deviceInfoSet,
        NULL,
        &GUID_DEVINTERFACE_RS485_FILTER,
        deviceIndex,
        &deviceInterfaceData)) {

        // Get required buffer size
        DWORD requiredSize = 0;
        SetupDiGetDeviceInterfaceDetail(
            deviceInfoSet,
            &deviceInterfaceData,
            NULL,
            0,
            &requiredSize,
            NULL
        );

        if (requiredSize > 0) {
            // Allocate buffer and get device path
            std::vector<BYTE> buffer(requiredSize);
            PSP_DEVICE_INTERFACE_DETAIL_DATA detailData = 
                reinterpret_cast<PSP_DEVICE_INTERFACE_DETAIL_DATA>(buffer.data());
            detailData->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA);

            SP_DEVINFO_DATA deviceInfoData;
            deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

            if (SetupDiGetDeviceInterfaceDetail(
                deviceInfoSet,
                &deviceInterfaceData,
                detailData,
                requiredSize,
                NULL,
                &deviceInfoData)) {

                DeviceInfo device;
                device.port = detailData->DevicePath;
                device.description = "AI-SLDAP RS485 Filter Driver";
                device.serialNumber = ""; // Would need additional queries
                device.driverVersion = "*******";
                device.isDriverLoaded = true;
                device.vendorId = 0x0403; // FTDI VID
                device.productId = 0x6001; // FTDI PID

                deviceList.push_back(device);
            }
        }

        deviceIndex++;
    }

    SetupDiDestroyDeviceInfoList(deviceInfoSet);

    return deviceList.empty() ? EnumerationResultNoDevicesFound : EnumerationResultSuccess;
}

//
// Configure System Settings
//
RS485_CONFIGURATION_RESULT RS485DriverInterface::configureSystemSettings(
    const std::string& commandKey, 
    uint64_t value) {
    
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return ConfigurationResultDeviceNotResponding;
    }

    // Check buffer before transmission
    RS485_BUFFER_RESULT bufferResult = checkBufferBeforeTransmission();
    if (bufferResult != BufferResultSuccess) {
        return ConfigurationResultBufferFull;
    }

    // Prepare IOCTL input
    RS485_SYSTEM_CONFIG_INPUT input;
    strncpy_s(input.CommandKey, sizeof(input.CommandKey), commandKey.c_str(), 4);
    input.Value = value;
    input.FunctionCode = RS485_FUNCTION_ASSIGN_DATA;
    input.SlaveAddress = RS485_BROADCAST_ADDRESS;
    input.Reserved[0] = 0;
    input.Reserved[1] = 0;

    // Send IOCTL
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_pImpl->driverHandle,
        IOCTL_RS485_CONFIGURE_SYSTEM,
        &input,
        sizeof(input),
        NULL,
        0,
        &bytesReturned,
        NULL
    );

    if (!result) {
        DWORD error = GetLastError();
        return mapWindowsErrorToConfigurationResult(error);
    }

    // Update current slave address if S001 command
    if (commandKey == "S001") {
        m_currentSlaveAddress = static_cast<uint8_t>(value & 0xFF);
    }

    return ConfigurationResultSuccess;
}

//
// Configure User Settings
//
RS485_CONFIGURATION_RESULT RS485DriverInterface::configureUserSettings(
    const std::string& commandKey, 
    uint64_t value) {
    
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return ConfigurationResultDeviceNotResponding;
    }

    // Check buffer before transmission
    RS485_BUFFER_RESULT bufferResult = checkBufferBeforeTransmission();
    if (bufferResult != BufferResultSuccess) {
        return ConfigurationResultBufferFull;
    }

    // Prepare IOCTL input
    RS485_USER_CONFIG_INPUT input;
    strncpy_s(input.CommandKey, sizeof(input.CommandKey), commandKey.c_str(), 4);
    input.Value = value;
    input.SlaveAddress = m_currentSlaveAddress;
    input.Reserved[0] = 0;
    input.Reserved[1] = 0;
    input.Reserved[2] = 0;

    // Send IOCTL
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_pImpl->driverHandle,
        IOCTL_RS485_CONFIGURE_USER,
        &input,
        sizeof(input),
        NULL,
        0,
        &bytesReturned,
        NULL
    );

    if (!result) {
        DWORD error = GetLastError();
        return mapWindowsErrorToConfigurationResult(error);
    }

    return ConfigurationResultSuccess;
}

//
// Request Data
//
RS485_REQUEST_RESULT RS485DriverInterface::requestData(
    const std::string& dataKey, 
    const RequestOptions* options) {
    
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return RequestResultDeviceNotResponding;
    }

    // Check buffer before transmission
    RS485_BUFFER_RESULT bufferResult = checkBufferBeforeTransmission();
    if (bufferResult != BufferResultSuccess) {
        return RequestResultBufferFull;
    }

    // Prepare IOCTL input
    RS485_DATA_REQUEST_INPUT input;
    strncpy_s(input.DataKey, sizeof(input.DataKey), dataKey.c_str(), 4);
    input.SlaveAddress = m_currentSlaveAddress;
    input.Reserved[0] = 0;
    input.Reserved[1] = 0;
    input.Reserved[2] = 0;

    // Send IOCTL
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_pImpl->driverHandle,
        IOCTL_RS485_REQUEST_DATA,
        &input,
        sizeof(input),
        NULL,
        0,
        &bytesReturned,
        NULL
    );

    if (!result) {
        DWORD error = GetLastError();
        if (error == ERROR_TIMEOUT) {
            return RequestResultRequestTimeout;
        }
        return RequestResultInvalidRequest;
    }

    return RequestResultSuccess;
}

//
// Receive Slave Response
//
RS485_RESPONSE_RESULT RS485DriverInterface::receiveSlaveResponse(
    uint8_t slaveAddress, 
    uint8_t responseData[12], 
    uint32_t timeout) {
    
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return ResponseResultNoDataAvailable;
    }

    if (responseData == NULL) {
        return ResponseResultInvalidResponse;
    }

    // Prepare IOCTL input/output
    RS485_RESPONSE_RECEIVE_INPUT input;
    input.SlaveAddress = slaveAddress;
    input.TimeoutMs = timeout;
    input.Reserved[0] = 0;
    input.Reserved[1] = 0;
    input.Reserved[2] = 0;

    RS485_RESPONSE_RECEIVE_OUTPUT output;
    RtlZeroMemory(&output, sizeof(output));

    // Send IOCTL
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_pImpl->driverHandle,
        IOCTL_RS485_RECEIVE_RESPONSE,
        &input,
        sizeof(input),
        &output,
        sizeof(output),
        &bytesReturned,
        NULL
    );

    if (!result) {
        DWORD error = GetLastError();
        if (error == ERROR_TIMEOUT) {
            return ResponseResultResponseTimeout;
        }
        return ResponseResultNoDataAvailable;
    }

    if (!output.IsDataReady) {
        return ResponseResultNoDataAvailable;
    }

    // Copy response data
    memcpy(responseData, output.ResponseData, 12);

    return ResponseResultSuccess;
}

//
// Get Buffer Status
//
RS485_BUFFER_RESULT RS485DriverInterface::getBufferStatus(BufferStatus& status) {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    if (!m_pImpl->isOpen) {
        return BufferResultBufferNotInitialized;
    }

    RS485_BUFFER_STATUS driverStatus;
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_pImpl->driverHandle,
        IOCTL_RS485_GET_BUFFER_STATUS,
        NULL,
        0,
        &driverStatus,
        sizeof(driverStatus),
        &bytesReturned,
        NULL
    );

    if (!result) {
        return BufferResultBufferNotInitialized;
    }

    // Convert driver status to interface status
    status.uplinkUsed = driverStatus.UplinkUsed;
    status.uplinkTotal = driverStatus.UplinkTotal;
    status.downlinkUsed = driverStatus.DownlinkUsed;
    status.downlinkTotal = driverStatus.DownlinkTotal;
    status.payloadSize = driverStatus.PayloadSize;
    status.isUplinkFull = driverStatus.IsUplinkFull;
    status.isDownlinkFull = driverStatus.IsDownlinkFull;
    status.isOverflowDetected = driverStatus.IsOverflowDetected;
    status.totalBufferBytes = driverStatus.TotalBufferBytes;

    return BufferResultSuccess;
}

//
// Check Buffer Before Transmission
//
RS485_BUFFER_RESULT RS485DriverInterface::checkBufferBeforeTransmission() {
    BufferStatus status;
    RS485_BUFFER_RESULT result = getBufferStatus(status);
    if (result != BufferResultSuccess) {
        return result;
    }

    if (status.isUplinkFull) {
        return BufferResultBufferOverflow;
    }

    return BufferResultSuccess;
}

//
// Get Error String
//
const char* RS485DriverInterface::getErrorString(RS485_ERROR error) const {
    switch (error) {
        case RS485_SUCCESS: return "Success";
        case RS485_CONNECTION_ERROR: return "Connection error";
        case RS485_DEVICE_NOT_FOUND: return "Device not found";
        case RS485_DEVICE_BUSY: return "Device busy";
        case RS485_PORT_NOT_AVAILABLE: return "Port not available";
        case RS485_INVALID_PARAMETER: return "Invalid parameter";
        case RS485_INSUFFICIENT_BUFFER: return "Insufficient buffer";
        case RS485_BUFFER_OVERFLOW: return "Buffer overflow";
        case RS485_PROTOCOL_ERROR: return "Protocol error";
        case RS485_CRC_ERROR: return "CRC error";
        case RS485_TIMEOUT_ERROR: return "Timeout error";
        case RS485_INVALID_COMMAND_KEY: return "Invalid command key";
        case RS485_INVALID_SLAVE_ADDRESS: return "Invalid slave address";
        default: return "Unknown error";
    }
}

//
// Map Windows Error to Connection Result
//
RS485_CONNECTION_RESULT RS485DriverInterface::mapWindowsErrorToConnectionResult(DWORD error) {
    switch (error) {
        case ERROR_FILE_NOT_FOUND:
        case ERROR_PATH_NOT_FOUND:
            return ConnectionResultPortNotFound;
        case ERROR_ACCESS_DENIED:
            return ConnectionResultPortAccessDenied;
        case ERROR_SHARING_VIOLATION:
            return ConnectionResultPortAlreadyOpen;
        case ERROR_TIMEOUT:
            return ConnectionResultTimeout;
        default:
            return ConnectionResultConnectionFailed;
    }
}

//
// Map Windows Error to Configuration Result
//
RS485_CONFIGURATION_RESULT RS485DriverInterface::mapWindowsErrorToConfigurationResult(DWORD error) {
    switch (error) {
        case ERROR_INVALID_PARAMETER:
            return ConfigurationResultInvalidCommand;
        case ERROR_TIMEOUT:
            return ConfigurationResultTimeout;
        case ERROR_DEVICE_NOT_READY:
            return ConfigurationResultDeviceNotResponding;
        case ERROR_BUFFER_OVERFLOW:
            return ConfigurationResultBufferFull;
        default:
            return ConfigurationResultConfigurationFailed;
    }
}

//
// Data Format Utility Functions
//
uint64_t RS485DriverInterface::encodeInteger(uint32_t value) {
    return static_cast<uint64_t>(value);
}

uint64_t RS485DriverInterface::encodeDualIntegers(uint32_t value1, uint32_t value2) {
    return static_cast<uint64_t>(value1) | (static_cast<uint64_t>(value2) << 32);
}

uint64_t RS485DriverInterface::encodeFloat(float value) {
    uint32_t bits;
    memcpy(&bits, &value, sizeof(float));
    return static_cast<uint64_t>(bits);
}

uint64_t RS485DriverInterface::encodeDouble(double value) {
    uint64_t bits;
    memcpy(&bits, &value, sizeof(double));
    return bits;
}

uint32_t RS485DriverInterface::decodeInteger(uint64_t payload) {
    return static_cast<uint32_t>(payload & 0xFFFFFFFFULL);
}

std::pair<uint32_t, uint32_t> RS485DriverInterface::decodeDualIntegers(uint64_t payload) {
    uint32_t value1 = static_cast<uint32_t>(payload & 0xFFFFFFFFULL);
    uint32_t value2 = static_cast<uint32_t>((payload >> 32) & 0xFFFFFFFFULL);
    return std::make_pair(value1, value2);
}

float RS485DriverInterface::decodeFloat(uint64_t payload) {
    uint32_t bits = static_cast<uint32_t>(payload & 0xFFFFFFFFULL);
    float value;
    memcpy(&value, &bits, sizeof(float));
    return value;
}

double RS485DriverInterface::decodeDouble(uint64_t payload) {
    double value;
    memcpy(&value, &payload, sizeof(double));
    return value;
}

//
// RS485DataHandler Implementation
//
RS485_CONFIGURATION_RESULT RS485DataHandler::configureIntegerSetting(
    RS485DriverInterface& driver,
    const std::string& key,
    uint32_t value,
    uint32_t minValue,
    uint32_t maxValue) {

    if (value < minValue || value > maxValue) {
        return ConfigurationResultInvalidValue;
    }

    uint64_t encodedValue = driver.encodeInteger(value);
    return driver.configureUserSettings(key, encodedValue);
}

RS485_CONFIGURATION_RESULT RS485DataHandler::configureDualIntegerSetting(
    RS485DriverInterface& driver,
    const std::string& key,
    uint32_t value1,
    uint32_t value2) {

    uint64_t encodedValue = driver.encodeDualIntegers(value1, value2);
    return driver.configureUserSettings(key, encodedValue);
}

//
// PayloadDataExtractor Implementation
//
std::string PayloadDataExtractor::extractKey(const uint8_t* payload) {
    if (payload == NULL) {
        return "";
    }

    char keyBuffer[5] = {0};
    memcpy(keyBuffer, payload, 4);
    return std::string(keyBuffer);
}

uint32_t PayloadDataExtractor::extractInteger(const uint8_t* payload) {
    if (payload == NULL) {
        return 0;
    }

    return (static_cast<uint32_t>(payload[4]) << 0) |
           (static_cast<uint32_t>(payload[5]) << 8) |
           (static_cast<uint32_t>(payload[6]) << 16) |
           (static_cast<uint32_t>(payload[7]) << 24);
}

void PayloadDataExtractor::storeKey(uint8_t* payload, const std::string& key) {
    if (payload == NULL) {
        return;
    }

    memset(payload, 0, 4);
    size_t copyLen = std::min(key.length(), static_cast<size_t>(4));
    memcpy(payload, key.c_str(), copyLen);
}

void PayloadDataExtractor::storeInteger(uint8_t* payload, uint32_t value) {
    if (payload == NULL) {
        return;
    }

    payload[4] = (value >> 0) & 0xFF;
    payload[5] = (value >> 8) & 0xFF;
    payload[6] = (value >> 16) & 0xFF;
    payload[7] = (value >> 24) & 0xFF;
    payload[8] = payload[9] = payload[10] = payload[11] = 0x00;
}
