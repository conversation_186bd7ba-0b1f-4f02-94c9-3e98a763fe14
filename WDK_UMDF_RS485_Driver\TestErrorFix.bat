@echo off
REM ===================================================================
REM Test Script for Error 3758096943 Fix
REM ===================================================================

echo ===================================================================
echo RS485 Driver Installation Error Fix Test
echo ===================================================================
echo.
echo This script tests the enhanced error handling for driver installation.
echo.

echo Current test signing status:
bcdedit /enum {current} | findstr /i testsigning
echo.

echo Checking if running as administrator...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo WARNING: Not running as administrator!
    echo Some installation features may not work.
    echo.
) else (
    echo ✓ Running as administrator
    echo.
)

echo Testing enhanced installer with better error handling...
echo ===================================================================
echo.
echo The installer now provides specific guidance for:
echo - Digital signature issues (Error 0xE0000235)
echo - Invalid parameters (Error 87)
echo - Access denied (Error 5)
echo - File not found (Error 2)
echo - General installation failures (Error 3758096943)
echo.

echo To enable test signing for development:
echo 1. Run as administrator: bcdedit /set testsigning on
echo 2. Reboot computer
echo 3. Run installer again
echo.

echo Ready to test installer? (Press any key to continue)
pause >nul

echo.
echo Running installer...
echo ===================================================================
.\FinalOutput\RS485DriverInstaller.exe

echo.
echo ===================================================================
echo Test completed!
echo.
echo If you saw specific error guidance instead of just "Error 3758096943",
echo then the fix is working correctly!
echo.

pause
