@echo off
echo ===================================================================
echo RS485 Driver Certificate Creation (Secure Boot Compatible)
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Creating self-signed certificate for driver signing...
echo.

REM Create certificate using PowerShell with proper parameters
powershell -ExecutionPolicy Bypass -Command "$cert = New-SelfSignedCertificate -Subject 'CN=RS485 Driver Certificate' -Type CodeSigningCert -KeyUsage DigitalSignature -FriendlyName 'RS485 Driver Development Certificate' -CertStoreLocation 'Cert:\LocalMachine\My' -KeyLength 2048 -Provider 'Microsoft Enhanced RSA and AES Cryptographic Provider' -KeyExportPolicy Exportable; Write-Host 'Certificate created successfully in LocalMachine\My store'; $certPath = 'RS485DriverCert.cer'; Export-Certificate -Cert $cert -FilePath $certPath -Force; Write-Host ('Certificate exported to: ' + $certPath); Import-Certificate -FilePath $certPath -CertStoreLocation 'Cert:\LocalMachine\Root'; Import-Certificate -FilePath $certPath -CertStoreLocation 'Cert:\LocalMachine\TrustedPublisher'; Write-Host 'Certificate installed to Trusted Root and Trusted Publishers'; Write-Host ('Certificate thumbprint: ' + $cert.Thumbprint); $cert.Thumbprint"

echo.
echo Certificate creation completed.
echo.
echo This certificate allows driver signing without requiring test signing mode.
echo The certificate is installed in the trusted stores, making it valid for
echo driver installation even with Secure Boot enabled.
echo.

pause
