//
// RS485 Protocol Processing Implementation
// ZES Protocol frame processing and CRC calculation
//

#include "RS485FilterDriver.h"

//
// CRC8 Lookup Table for polynomial 0x97
//
static const UINT8 RS485_CRC8_TABLE[256] = {
    0x00, 0x97, 0xB9, 0x2E, 0xE5, 0x72, 0x5C, 0xCB,
    0x5D, 0xCA, 0xE4, 0x73, 0xB8, 0x2F, 0x01, 0x96,
    0xBA, 0x2D, 0x03, 0x94, 0x5F, 0xC8, 0xE6, 0x71,
    0xE7, 0x70, 0x5E, 0xC9, 0x02, 0x95, 0xBB, 0x2C,
    0xE3, 0x74, 0x5A, 0xCD, 0x06, 0x91, 0xBF, 0x28,
    0xBE, 0x29, 0x07, 0x90, 0x5B, 0xCC, 0xE2, 0x75,
    0x59, 0xCE, 0xE0, 0x77, 0xBC, 0x2B, 0x05, 0x92,
    0x04, 0x93, 0xBD, 0x2A, 0xE1, 0x76, 0x58, 0xCF,
    0x51, 0xC6, 0xE8, 0x7F, 0xB4, 0x23, 0x0D, 0x9A,
    0x0C, 0x9B, 0xB5, 0x22, 0xE9, 0x7E, 0x50, 0xC7,
    0xEB, 0x7C, 0x52, 0xC5, 0x0E, 0x99, 0xB7, 0x20,
    0xB6, 0x21, 0x0F, 0x98, 0x53, 0xC4, 0xEA, 0x7D,
    0xB2, 0x25, 0x0B, 0x9C, 0x57, 0xC0, 0xEE, 0x79,
    0xEF, 0x78, 0x56, 0xC1, 0x0A, 0x9D, 0xB3, 0x24,
    0x08, 0x9F, 0xB1, 0x26, 0xED, 0x7A, 0x54, 0xC3,
    0x55, 0xC2, 0xEC, 0x7B, 0xB0, 0x27, 0x09, 0x9E,
    0xA2, 0x35, 0x1B, 0x8C, 0x47, 0xD0, 0xFE, 0x69,
    0xFF, 0x68, 0x46, 0xD1, 0x1A, 0x8D, 0xA3, 0x34,
    0x18, 0x8F, 0xA1, 0x36, 0xFD, 0x6A, 0x44, 0xD3,
    0x45, 0xD2, 0xFC, 0x6B, 0xA0, 0x37, 0x19, 0x8E,
    0x41, 0xD6, 0xF8, 0x6F, 0xA4, 0x33, 0x1D, 0x8A,
    0x1C, 0x8B, 0xA5, 0x32, 0xF9, 0x6E, 0x40, 0xD7,
    0xFB, 0x6C, 0x42, 0xD5, 0x1E, 0x89, 0xA7, 0x30,
    0xA6, 0x31, 0x1F, 0x88, 0x43, 0xD4, 0xFA, 0x6D,
    0xF3, 0x64, 0x4A, 0xDD, 0x16, 0x81, 0xAF, 0x38,
    0xAE, 0x39, 0x17, 0x80, 0x4B, 0xDC, 0xF2, 0x65,
    0x49, 0xDE, 0xF0, 0x67, 0xAC, 0x3B, 0x15, 0x82,
    0x14, 0x83, 0xAD, 0x3A, 0xF1, 0x66, 0x48, 0xDF,
    0x10, 0x87, 0xA9, 0x3E, 0xF5, 0x62, 0x4C, 0xDB,
    0x4D, 0xDA, 0xF4, 0x63, 0xA8, 0x3F, 0x11, 0x86,
    0xAA, 0x3D, 0x13, 0x84, 0x4F, 0xD8, 0xF6, 0x61,
    0xF7, 0x60, 0x4E, 0xD9, 0x12, 0x85, 0xAB, 0x3C
};

//
// Calculate CRC8 for data buffer
//
UINT8 RS485CalculateCRC8(
    _In_reads_bytes_(Length) const UINT8* Data,
    _In_ SIZE_T Length
)
{
    UINT8 crc = RS485_CRC8_INITIAL_VALUE;
    SIZE_T i;

    if (Data == NULL || Length == 0) {
        return crc;
    }

    for (i = 0; i < Length; i++) {
        crc = RS485_CRC8_TABLE[crc ^ Data[i]];
    }

    return crc;
}

//
// Verify CRC8 for RS485 frame
//
BOOLEAN RS485VerifyCRC8(
    _In_ const RS485_FRAME* Frame
)
{
    UINT8 calculatedCrc;

    if (Frame == NULL) {
        return FALSE;
    }

    // Calculate CRC for ID byte + payload (13 bytes total)
    calculatedCrc = RS485CalculateCRC8(&Frame->SlaveId, 13);

    return (calculatedCrc == Frame->Crc);
}

//
// Initialize Frame Processor
//
NTSTATUS RS485InitializeFrameProcessor(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext
)
{
    if (DeviceContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Initialize frame processing state
    DeviceContext->FrameState = FrameStateWaitingHeader;
    DeviceContext->BytesReceived = 0;
    RtlZeroMemory(&DeviceContext->CurrentFrame, sizeof(RS485_FRAME));

    RS485_DEBUG_PRINT("Frame processor initialized");
    return STATUS_SUCCESS;
}

//
// Cleanup Frame Processor
//
VOID RS485CleanupFrameProcessor(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext
)
{
    if (DeviceContext == NULL) {
        return;
    }

    // Reset frame processing state
    DeviceContext->FrameState = FrameStateWaitingHeader;
    DeviceContext->BytesReceived = 0;
    RtlZeroMemory(&DeviceContext->CurrentFrame, sizeof(RS485_FRAME));

    RS485_DEBUG_PRINT("Frame processor cleaned up");
}

//
// Process Incoming Byte (State Machine)
//
RS485_FRAME_PROCESS_RESULT RS485ProcessIncomingByte(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ UINT8 Byte
)
{
    RS485_FRAME_PROCESS_RESULT result = FrameProcessContinue;

    if (DeviceContext == NULL) {
        return FrameProcessFrameError;
    }

    // Acquire frame lock (UMDF uses WdfWaitLockAcquire instead of spinlocks)
    WdfWaitLockAcquire(DeviceContext->FrameLock, NULL);

    switch (DeviceContext->FrameState) {
        case FrameStateWaitingHeader:
            if (Byte == RS485_HEADER_BYTE) {
                DeviceContext->CurrentFrame.Header = Byte;
                DeviceContext->BytesReceived = 1;
                DeviceContext->FrameState = FrameStateReadingId;
                RS485_DEBUG_PRINT("Frame header detected");
            }
            // Stay in waiting state for other bytes
            break;

        case FrameStateReadingId:
            DeviceContext->CurrentFrame.SlaveId = Byte;
            DeviceContext->BytesReceived = 2;
            DeviceContext->FrameState = FrameStateReadingPayload;
            RS485_DEBUG_PRINT("Frame ID byte received: 0x%02X", Byte);
            break;

        case FrameStateReadingPayload:
            {
                SIZE_T payloadIndex = DeviceContext->BytesReceived - 2;
                if (payloadIndex < RS485_PAYLOAD_SIZE) {
                    DeviceContext->CurrentFrame.Payload[payloadIndex] = Byte;
                    DeviceContext->BytesReceived++;
                    
                    if (payloadIndex == RS485_PAYLOAD_SIZE - 1) {
                        DeviceContext->FrameState = FrameStateReadingCrc;
                        RS485_DEBUG_PRINT("Frame payload complete");
                    }
                } else {
                    // Payload overflow - reset state machine
                    DeviceContext->FrameState = FrameStateWaitingHeader;
                    DeviceContext->BytesReceived = 0;
                    result = FrameProcessFrameError;
                    RS485_ERROR_PRINT("Payload overflow detected");
                }
            }
            break;

        case FrameStateReadingCrc:
            DeviceContext->CurrentFrame.Crc = Byte;
            DeviceContext->BytesReceived++;
            DeviceContext->FrameState = FrameStateReadingTrailer;
            RS485_DEBUG_PRINT("Frame CRC received: 0x%02X", Byte);
            break;

        case FrameStateReadingTrailer:
            DeviceContext->CurrentFrame.Trailer = Byte;
            DeviceContext->BytesReceived++;
            
            if (Byte == RS485_TRAILER_BYTE && DeviceContext->BytesReceived == RS485_FRAME_SIZE) {
                DeviceContext->FrameState = FrameStateComplete;
                result = FrameProcessFrameReady;
                RS485_DEBUG_PRINT("Complete frame received");
            } else {
                // Invalid trailer or wrong frame size - reset state machine
                DeviceContext->FrameState = FrameStateWaitingHeader;
                DeviceContext->BytesReceived = 0;
                result = FrameProcessFrameError;
                RS485_ERROR_PRINT("Invalid frame trailer or size");
            }
            break;

        case FrameStateComplete:
        case FrameStateError:
        default:
            // Reset state machine
            DeviceContext->FrameState = FrameStateWaitingHeader;
            DeviceContext->BytesReceived = 0;
            result = FrameProcessFrameError;
            break;
    }

    // Release frame lock
    WdfWaitLockRelease(DeviceContext->FrameLock);

    return result;
}

//
// Process Complete Frame
//
NTSTATUS RS485ProcessCompleteFrame(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ const RS485_FRAME* Frame
)
{
    NTSTATUS status;

    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Verify CRC8
    if (!RS485VerifyCRC8(Frame)) {
        RS485_ERROR_PRINT("CRC verification failed");
        RS485UpdateErrorStatistics(&DeviceContext->ErrorStatistics, RS485_CRC_ERROR);
        
        // Schedule re-send request
        status = RS485ScheduleResendRequest(DeviceContext, Frame);
        return status;
    }

    // Route frame to appropriate API category
    status = RS485RouteFrameToAPICategory(DeviceContext, Frame);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to route frame: 0x%x", status);
        return status;
    }

    // Reset frame processor state for next frame
    DeviceContext->FrameState = FrameStateWaitingHeader;
    DeviceContext->BytesReceived = 0;

    return STATUS_SUCCESS;
}

//
// Build Frame from Payload
//
NTSTATUS RS485BuildFrame(
    _In_ const RS485_PAYLOAD* Payload,
    _In_ UINT8 FunctionCode,
    _In_ UINT8 DeviceAddress,
    _Out_ PRS485_FRAME Frame
)
{
    if (Payload == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Validate function code (3 bits)
    if (FunctionCode > 0x07) {
        return STATUS_INVALID_PARAMETER;
    }

    // Validate device address (5 bits)
    if (DeviceAddress > 0x1F) {
        return STATUS_INVALID_PARAMETER;
    }

    // Build frame
    Frame->Header = RS485_HEADER_BYTE;
    Frame->SlaveId = RS485_BUILD_ID_BYTE(FunctionCode, DeviceAddress);
    RtlCopyMemory(Frame->Payload, Payload, RS485_PAYLOAD_SIZE);
    Frame->Trailer = RS485_TRAILER_BYTE;

    // Calculate CRC8 for ID byte + payload
    Frame->Crc = RS485CalculateCRC8(&Frame->SlaveId, 13);

    RS485_DEBUG_PRINT("Frame built: Func=0x%X, Addr=0x%X, CRC=0x%02X",
                      FunctionCode, DeviceAddress, Frame->Crc);
    return STATUS_SUCCESS;
}

//
// Route Frame to API Category - Function moved to RS485ProtocolHandlers.cpp
// This is just a comment placeholder to maintain code organization
//

//
// Extract Payload Key
//
VOID RS485ExtractPayloadKey(
    _In_ const RS485_PAYLOAD* Payload,
    _Out_writes_(5) PCHAR Key
)
{
    if (Payload == NULL || Key == NULL) {
        return;
    }

    // Copy key and ensure null termination
    RtlCopyMemory(Key, Payload->Key, 4);
    Key[4] = '\0';
}

//
// Extract Payload Integer (32-bit from bytes 4-7)
//
UINT32 RS485ExtractPayloadInteger(
    _In_ const RS485_PAYLOAD* Payload
)
{
    UINT32 value;

    if (Payload == NULL) {
        return 0;
    }

    // Extract little-endian 32-bit integer from bytes 4-7
    value = (UINT32)Payload->Value[0] |
            ((UINT32)Payload->Value[1] << 8) |
            ((UINT32)Payload->Value[2] << 16) |
            ((UINT32)Payload->Value[3] << 24);

    return value;
}

//
// Store Payload Key
//
VOID RS485StorePayloadKey(
    _Out_ PRS485_PAYLOAD Payload,
    _In_z_ PCSTR Key
)
{
    SIZE_T keyLength;

    if (Payload == NULL || Key == NULL) {
        return;
    }

    // Clear key field
    RtlZeroMemory(Payload->Key, 4);

    // Copy key (up to 4 characters)
    keyLength = strlen(Key);
    if (keyLength > 4) {
        keyLength = 4;
    }

    RtlCopyMemory(Payload->Key, Key, keyLength);
}

//
// Store Payload Integer (32-bit to bytes 4-7, zero upper bytes)
//
VOID RS485StorePayloadInteger(
    _Out_ PRS485_PAYLOAD Payload,
    _In_ UINT32 Value
)
{
    if (Payload == NULL) {
        return;
    }

    // Store little-endian 32-bit integer in bytes 4-7
    Payload->Value[0] = (UINT8)(Value & 0xFF);
    Payload->Value[1] = (UINT8)((Value >> 8) & 0xFF);
    Payload->Value[2] = (UINT8)((Value >> 16) & 0xFF);
    Payload->Value[3] = (UINT8)((Value >> 24) & 0xFF);

    // Clear upper 4 bytes
    Payload->Value[4] = 0;
    Payload->Value[5] = 0;
    Payload->Value[6] = 0;
    Payload->Value[7] = 0;
}
