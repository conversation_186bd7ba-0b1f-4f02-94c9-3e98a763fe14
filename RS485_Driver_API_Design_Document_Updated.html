<!DOCTYPE html><html><head>
      <title>RS485_Driver_API_Design_Document_Updated</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="ai-sldap-rs485-driver-api-design-document">AI-SLDAP RS485 Driver API Design Document </h1>
<h2 id="executive-summary">Executive Summary </h2>
<p>This document presents a comprehensive API design for the AI-SLDAP RS485 Driver implementing the ZES proprietary protocol. Key features include:</p>
<ul>
<li><strong>Protocol-Compliant JSON Handling</strong>: Native JSON parsing for 12-byte payload key-value pairs</li>
<li><strong>Bidirectional Re-send Implementation</strong>: Full support for both master and slave initiated re-send requests</li>
<li><strong>Enhanced Broadcasting Safety</strong>: Single-slave enforcement with multiple device detection</li>
<li><strong>Proper Baud Rate Switching</strong>: Complete S002 implementation with acknowledgment mechanism</li>
<li><strong>Cross-Platform Data Consistency</strong>: IEEE 754 and little-endian standardization</li>
</ul>
<h2 id="table-of-contents">Table of Contents </h2>
<ol>
<li><a href="#1-introduction">Introduction</a></li>
<li><a href="#2-protocol-compliance-enhancements">Protocol Compliance Enhancements</a></li>
<li><a href="#3-api-overview">API Overview</a></li>
<li><a href="#4-protocol-implementation">Protocol Implementation</a></li>
<li><a href="#5-api-reference">API Reference</a></li>
<li><a href="#6-using-the-api">Using the API</a></li>
<li><a href="#7-additional-features">Additional Features</a></li>
<li><a href="#8-windows-driver-implementation-summary">Windows Driver Implementation Summary</a></li>
<li><a href="#9-frequently-asked-questions-faq">Frequently Asked Questions (FAQ)</a></li>
<li><a href="#10-conclusion">Conclusion</a></li>
</ol>
<h2 id="1-introduction">1. Introduction </h2>
<h3 id="11-purpose-and-protocol-alignment">1.1 Purpose and Protocol Alignment </h3>
<p>This document describes the API design for the AI-SLDAP RS485 Driver, implementing the ZES proprietary communication protocol for satellite On-Board Computer (OBC) applications. The design ensures full compliance with the RS485 Communication Software Protocol v1.1 specification.</p>
<p><strong>Key Protocol Compliance Features:</strong></p>
<ul>
<li><strong>JSON Payload Processing</strong>: Native support for key-value pair data in 12-byte payload</li>
<li><strong>Bidirectional Re-send</strong>: Both master and slave can initiate re-send requests</li>
<li><strong>Broadcast Acknowledgment</strong>: Proper S002 baud rate switch with acknowledgment waiting</li>
<li><strong>CRC8 Coverage</strong>: Correct implementation covering 13 bytes (ID to payload, excluding header)</li>
<li><strong>100ms Response Window</strong>: Configurable timeout with collision handling</li>
</ul>
<h3 id="12-scope-and-platform-support">1.2 Scope and Platform Support </h3>
<p>The API supports:</p>
<ul>
<li><strong>Windows</strong> (10, 11) using Windows Driver Kit (WDK) with User-Mode Driver Framework (UMDF 2)</li>
<li><strong>Linux</strong> (Ubuntu 18.04+, CentOS 7+, RHEL 8+) using kernel module implementation</li>
<li><strong>Cross-platform compatibility</strong> with identical API interfaces and data formats</li>
<li><strong>Real-time operation</strong> suitable for airborne and industrial control systems</li>
<li><strong>Multi-device support</strong> (up to 30 slave devices with collision avoidance)</li>
</ul>
<h3 id="13-key-design-features">1.3 Key Design Features </h3>
<ul>
<li><strong>Non-blocking Design</strong>: Critical for airborne environments with multiple concurrent tasks</li>
<li><strong>Protocol-Compliant Buffer Management</strong>: 12-byte payload-centric design (5×12 uplink, 10×12 downlink)</li>
<li><strong>Enhanced Error Recovery</strong>: Bidirectional re-send with configurable retry policies</li>
<li><strong>Cross-Platform Data Validation</strong>: IEEE 754 and little-endian standardization</li>
<li><strong>FIFO Guarantee</strong>: Strict First-In-First-Out ordering for reliable data transmission</li>
</ul>
<h2 id="2-protocol-compliance-enhancements">2. Protocol Compliance Enhancements </h2>
<h3 id="21-json-payload-processing">2.1 JSON Payload Processing </h3>
<p><strong>Protocol Requirement</strong>: The 12-byte payload contains key-value pair data</p>
<p><strong>Implementation Enhancement</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">PayloadDataExtractor</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// New: Native JSON parsing with validation</span>
    JsonResult <span class="token function">extractJson</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> nlohmann<span class="token double-colon punctuation">::</span>json<span class="token operator">&amp;</span> jsonData<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>string <span class="token function">jsonStr</span><span class="token punctuation">(</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">,</span>
                           <span class="token function">strnlen</span><span class="token punctuation">(</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token number">12</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-try">try</span> <span class="token punctuation">{</span>
            jsonData <span class="token operator">=</span> nlohmann<span class="token double-colon punctuation">::</span>json<span class="token double-colon punctuation">::</span><span class="token function">parse</span><span class="token punctuation">(</span>jsonStr<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-return">return</span> JsonResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-catch">catch</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> nlohmann<span class="token double-colon punctuation">::</span>json<span class="token double-colon punctuation">::</span>parse_error<span class="token operator">&amp;</span> e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Fallback to binary interpretation for non-JSON responses</span>
            <span class="token keyword keyword-return">return</span> JsonResult<span class="token double-colon punctuation">::</span>NOT_JSON_FORMAT<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Enhanced: Binary extraction with JSON awareness</span>
    <span class="token keyword keyword-template">template</span><span class="token operator">&lt;</span><span class="token keyword keyword-typename">typename</span> <span class="token class-name">T</span><span class="token operator">&gt;</span>
    ExtractionResult <span class="token function">extractValue</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> T<span class="token operator">&amp;</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// First try JSON parsing</span>
        nlohmann<span class="token double-colon punctuation">::</span>json jsonData<span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">extractJson</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> jsonData<span class="token punctuation">)</span> <span class="token operator">==</span> JsonResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> <span class="token function">extractFromJson</span><span class="token punctuation">(</span>jsonData<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Fallback to binary extraction</span>
        <span class="token keyword keyword-return">return</span> <span class="token function">extractBinary</span><span class="token punctuation">(</span>payload <span class="token operator">+</span> <span class="token number">4</span><span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Skip 4-byte key</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="22-bidirectional-re-send-implementation">2.2 Bidirectional Re-send Implementation </h3>
<p><strong>Protocol Requirement</strong>: Both master and slave can transmit re-send request frame (function code 0b000)</p>
<p><strong>Enhanced Implementation</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">ResendManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> m_maxRetries <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> m_retryDelayMs <span class="token operator">=</span> <span class="token number">50</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> m_exponentialBackoff <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Master-initiated re-send (existing)</span>
    ConfigurationResult <span class="token function">requestResend</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> frameSequence<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        ResendFrame frame<span class="token punctuation">;</span>
        frame<span class="token punctuation">.</span>functionCode <span class="token operator">=</span> <span class="token number">0b000</span><span class="token punctuation">;</span>
        frame<span class="token punctuation">.</span>slaveAddress <span class="token operator">=</span> slaveAddress<span class="token punctuation">;</span>
        frame<span class="token punctuation">.</span>sequenceNumber <span class="token operator">=</span> frameSequence<span class="token punctuation">;</span>

        <span class="token keyword keyword-return">return</span> <span class="token function">transmitFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// New: Slave-initiated re-send handling</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">handleSlaveResendRequest</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> ResendFrame<span class="token operator">&amp;</span> request<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Locate original frame in transmission history</span>
        <span class="token keyword keyword-auto">auto</span> originalFrame <span class="token operator">=</span> <span class="token function">findFrameBySequence</span><span class="token punctuation">(</span>request<span class="token punctuation">.</span>sequenceNumber<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>originalFrame<span class="token punctuation">.</span><span class="token function">has_value</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Retransmit original frame</span>
            <span class="token function">retransmitFrame</span><span class="token punctuation">(</span>originalFrame<span class="token punctuation">.</span><span class="token function">value</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
            <span class="token comment">// Frame not found in history - send error response</span>
            <span class="token function">sendErrorResponse</span><span class="token punctuation">(</span>request<span class="token punctuation">.</span>slaveAddress<span class="token punctuation">,</span> ErrorCode<span class="token double-colon punctuation">::</span>FRAME_NOT_FOUND<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Enhanced: Configurable retry policy</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">setRetryPolicy</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> maxRetries<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> delayMs<span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span> exponentialBackoff <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        m_maxRetries <span class="token operator">=</span> maxRetries<span class="token punctuation">;</span>
        m_retryDelayMs <span class="token operator">=</span> delayMs<span class="token punctuation">;</span>
        m_exponentialBackoff <span class="token operator">=</span> exponentialBackoff<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="23-enhanced-broadcasting-safety">2.3 Enhanced Broadcasting Safety </h3>
<p><strong>Protocol Requirement</strong>: Only use broadcast frame to assign slave address with single-slave limitation</p>
<p><strong>Improved Implementation</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">BroadcastManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-bool">bool</span> m_strictSingleSlaveMode <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Enhanced: Active device detection</span>
    DetectionResult <span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span> respondingAddresses<span class="token punctuation">;</span>

        <span class="token comment">// Send ping to all possible addresses (1-31)</span>
        <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> addr <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span> addr <span class="token operator">&lt;=</span> <span class="token number">31</span><span class="token punctuation">;</span> addr<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            PingFrame ping<span class="token punctuation">;</span>
            ping<span class="token punctuation">.</span>targetAddress <span class="token operator">=</span> addr<span class="token punctuation">;</span>
            ping<span class="token punctuation">.</span>timeout <span class="token operator">=</span> <span class="token number">50</span><span class="token punctuation">;</span> <span class="token comment">// Short timeout for detection</span>

            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">sendPing</span><span class="token punctuation">(</span>ping<span class="token punctuation">)</span> <span class="token operator">==</span> PingResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                respondingAddresses<span class="token punctuation">.</span><span class="token function">push_back</span><span class="token punctuation">(</span>addr<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>respondingAddresses<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token function">logMultipleDevicesDetected</span><span class="token punctuation">(</span>respondingAddresses<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-return">return</span> DetectionResult<span class="token double-colon punctuation">::</span>MULTIPLE_DEVICES_DETECTED<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-return">return</span> DetectionResult<span class="token double-colon punctuation">::</span>SINGLE_DEVICE_CONFIRMED<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Enhanced: Strict broadcasting with validation</span>
    ConfigurationResult <span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span> <span class="token punctuation">{</span>
        <span class="token comment">// Mandatory multiple device check for S-series commands</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_strictSingleSlaveMode<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            DetectionResult detection <span class="token operator">=</span> <span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>detection <span class="token operator">==</span> DetectionResult<span class="token double-colon punctuation">::</span>MULTIPLE_DEVICES_DETECTED<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>MULTIPLE_DEVICES_ERROR<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Proceed with broadcast</span>
        <span class="token keyword keyword-return">return</span> <span class="token function">sendBroadcastCommand</span><span class="token punctuation">(</span>commandKey<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="24-s002-baud-rate-switch-implementation">2.4 S002 Baud Rate Switch Implementation </h3>
<p><strong>Protocol Requirement</strong>: Slave starts at 9600, switches after broadcast S002, sends acknowledgment at new rate</p>
<p><strong>Complete Implementation</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">BaudRateManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> m_currentBaudRate <span class="token operator">=</span> <span class="token number">9600</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> m_waitingForAck <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    ConfigurationResult <span class="token function">setBaudRate</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> newBaudRate<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Validate baud rate</span>
        std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span> validRates <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token number">9600</span><span class="token punctuation">,</span> <span class="token number">19200</span><span class="token punctuation">,</span> <span class="token number">38400</span><span class="token punctuation">,</span> <span class="token number">57600</span><span class="token punctuation">,</span> <span class="token number">115200</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span><span class="token function">find</span><span class="token punctuation">(</span>validRates<span class="token punctuation">.</span><span class="token function">begin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> validRates<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> newBaudRate<span class="token punctuation">)</span> <span class="token operator">==</span> validRates<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>INVALID_PARAMETER<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Send S002 broadcast at current rate</span>
        ConfigurationResult result <span class="token operator">=</span> <span class="token function">sendBroadcastCommand</span><span class="token punctuation">(</span><span class="token string">"S002"</span><span class="token punctuation">,</span> newBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Switch master to new rate</span>
        result <span class="token operator">=</span> <span class="token function">reconfigureHardwareBaudRate</span><span class="token punctuation">(</span>newBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Revert to original rate</span>
            <span class="token function">reconfigureHardwareBaudRate</span><span class="token punctuation">(</span>m_currentBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>HARDWARE_ERROR<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Wait for acknowledgment at new rate</span>
        m_waitingForAck <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
        AckResult ackResult <span class="token operator">=</span> <span class="token function">waitForBaudRateAck</span><span class="token punctuation">(</span>newBaudRate<span class="token punctuation">,</span> <span class="token number">500</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 500ms timeout</span>
        m_waitingForAck <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>ackResult <span class="token operator">==</span> AckResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            m_currentBaudRate <span class="token operator">=</span> newBaudRate<span class="token punctuation">;</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
            <span class="token comment">// Revert to original rate on failure</span>
            <span class="token function">reconfigureHardwareBaudRate</span><span class="token punctuation">(</span>m_currentBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>TIMEOUT_ERROR<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="25-crc8-implementation-correction">2.5 CRC8 Implementation Correction </h3>
<p><strong>Protocol Requirement</strong>: CRC8 covers 13 bytes data from ID to data payload (excluding header byte)</p>
<p><strong>Corrected Implementation</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">CRC8Calculator</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> CRC8_POLYNOMIAL <span class="token operator">=</span> <span class="token number">0x97</span><span class="token punctuation">;</span> <span class="token comment">// Protocol-specified polynomial</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token function">calculateCRC8</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> data<span class="token punctuation">,</span> size_t length<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> crc <span class="token operator">=</span> <span class="token number">0x00</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span>size_t i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            crc <span class="token operator">^=</span> data<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> bit <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> bit <span class="token operator">&lt;</span> <span class="token number">8</span><span class="token punctuation">;</span> bit<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>crc <span class="token operator">&amp;</span> <span class="token number">0x80</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                    crc <span class="token operator">=</span> <span class="token punctuation">(</span>crc <span class="token operator">&lt;&lt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">^</span> CRC8_POLYNOMIAL<span class="token punctuation">;</span>
                <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
                    crc <span class="token operator">&lt;&lt;=</span> <span class="token number">1</span><span class="token punctuation">;</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-return">return</span> crc<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Protocol-compliant frame CRC calculation</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token function">calculateFrameCRC</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// CRC covers 13 bytes: ID byte + 12-byte payload (excluding header 0xAA)</span>
        <span class="token keyword keyword-return">return</span> <span class="token function">calculateCRC8</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>frame<span class="token punctuation">.</span>id_byte<span class="token punctuation">,</span> <span class="token number">13</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-bool">bool</span> <span class="token function">validateFrame</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> calculatedCRC <span class="token operator">=</span> <span class="token function">calculateFrameCRC</span><span class="token punctuation">(</span>frame<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span>calculatedCRC <span class="token operator">==</span> frame<span class="token punctuation">.</span>crc8<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="26-100ms-response-window-implementation">2.6 100ms Response Window Implementation </h3>
<p><strong>Protocol Requirement</strong>: 100ms response window with strict collision avoidance rules</p>
<p><strong>Enhanced Implementation</strong>:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">ResponseWindowManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint32_t">uint32_t</span> DEFAULT_RESPONSE_WINDOW_MS <span class="token operator">=</span> <span class="token number">100</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> m_responseWindowMs <span class="token operator">=</span> DEFAULT_RESPONSE_WINDOW_MS<span class="token punctuation">;</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Configurable response window for different environments</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">setResponseWindow</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> windowMs<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        m_responseWindowMs <span class="token operator">=</span> windowMs<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    ResponseResult <span class="token function">waitForResponse</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-auto">auto</span> startTime <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>steady_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-while">while</span> <span class="token punctuation">(</span><span class="token boolean">true</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-auto">auto</span> currentTime <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>steady_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-auto">auto</span> elapsedMs <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">duration_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>milliseconds<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>
                currentTime <span class="token operator">-</span> startTime<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">count</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>elapsedMs <span class="token operator">&gt;=</span> m_responseWindowMs<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token keyword keyword-return">return</span> ResponseResult<span class="token double-colon punctuation">::</span>TIMEOUT_ERROR<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>

            <span class="token comment">// Check for available response</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">checkResponseAvailable</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token keyword keyword-return">return</span> <span class="token function">retrieveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>

            <span class="token comment">// Small delay to prevent busy waiting</span>
            std<span class="token double-colon punctuation">::</span>this_thread<span class="token double-colon punctuation">::</span><span class="token function">sleep_for</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token function">milliseconds</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h2 id="3-api-overview">3. API Overview </h2>
<h3 id="31-enhanced-error-management-system">3.1 Enhanced Error Management System </h3>
<p>The API uses <strong>specific result types</strong> for different functional categories:</p>
<ul>
<li><strong><code>ConnectionResult</code></strong>: For port opening/closing operations</li>
<li><strong><code>BufferResult</code></strong>: For buffer management operations</li>
<li><strong><code>ConfigurationResult</code></strong>: For device configuration operations</li>
<li><strong><code>RequestResult</code>/<code>ResponseResult</code></strong>: For data request/response operations</li>
<li><strong><code>HardwareResult</code>/<code>PerformanceResult</code>/<code>LineResult</code></strong>: For status monitoring operations</li>
<li><strong><code>EnumerationResult</code>/<code>DetectionResult</code></strong>: For device discovery operations</li>
</ul>
<p><strong>Benefits:</strong></p>
<ol>
<li><strong>Enhanced Code Readability</strong>: Function signatures are self-documenting</li>
<li><strong>Intelligent Error Recovery</strong>: Automatic categorization enables smart retry strategies</li>
<li><strong>Improved Type Safety</strong>: Compiler catches incompatible result type comparisons</li>
<li><strong>Better Maintenance</strong>: Organized error handling reduces cross-functional interference</li>
</ol>
<h3 id="32-hardware-requirements">3.2 Hardware Requirements </h3>
<ul>
<li><strong>USB-RS485-WE-1800-BT FTDI converter</strong> for PC-side interface</li>
<li><strong>ZM-AISL-01 (FPGA) board</strong> as slave device</li>
<li><strong>Single twisted pair RS485 cable</strong> in BUS topology</li>
<li><strong>Maximum 32 devices</strong> supported on the bus (30 slaves + 1 master)</li>
</ul>
<h3 id="33-enhanced-driver-architecture">3.3 Enhanced Driver Architecture </h3>
<p>The implementation follows the OSI model adapted for ZM-AISL-01 protocol stack:</p>
<ul>
<li><strong>Physical Layer</strong>: EIA/TIA-485 (RS485) via USB-RS485 converter with FTDI integration</li>
<li><strong>Data Link Layer</strong>: ZES proprietary protocol implemented as Windows User-Mode Driver Framework (UMDF 2) with buffer management and non-blocking operations</li>
<li><strong>Application Layer</strong>: User Application (via ZES driver API) with cross-platform compatibility and type-safe data handling</li>
</ul>
<h3 id="34-api-categories-with-protocol-compliance">3.4 API Categories with Protocol Compliance </h3>
<p>The API design follows the ZES driver API classification with five key components:</p>
<h4 id="341-zes-driver-api-classification-and-function-code-correspondence">3.4.1 ZES Driver API Classification and Function Code Correspondence </h4>
<p><strong>Critical Design Principle: Function Code to API Category Mapping</strong></p>
<p>The five API categories directly correspond to the ZES protocol function codes in the ID byte:</p>
<table>
<thead>
<tr>
<th style="text-align:center">Function Code</th>
<th style="text-align:center">Binary</th>
<th style="text-align:left">Description</th>
<th style="text-align:left">API Category</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center"><strong>0b111</strong></td>
<td style="text-align:center">0b111</td>
<td style="text-align:left">Assign data</td>
<td style="text-align:left"><strong>Master Broadcasting API</strong> + <strong>Master Assign Data API</strong></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b110</strong></td>
<td style="text-align:center">0b110</td>
<td style="text-align:left">Request data</td>
<td style="text-align:left"><strong>Master Request API</strong></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b010</strong></td>
<td style="text-align:center">0b010</td>
<td style="text-align:left">Response to Assign</td>
<td style="text-align:left"><strong>Slave Response API</strong></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b001</strong></td>
<td style="text-align:center">0b001</td>
<td style="text-align:left">Response to Request</td>
<td style="text-align:left"><strong>Slave Response API</strong></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b000</strong></td>
<td style="text-align:center">0b000</td>
<td style="text-align:left">Re-send request</td>
<td style="text-align:left"><strong>Error Handle API</strong></td>
</tr>
</tbody>
</table>
<p><strong>Function Code Processing Logic:</strong><br>
The driver automatically determines which API category to use based on the function code in the ID byte:</p>
<ul>
<li><strong>0b111 (Assign)</strong>: Routes to Broadcasting API (S-series) or Assign Data API (U/W-series)</li>
<li><strong>0b110 (Request)</strong>: Routes to Master Request API (A-series)</li>
<li><strong>0b010/0b001 (Responses)</strong>: Routes to Slave Response API</li>
<li><strong>0b000 (Re-send)</strong>: Routes to Error Handle API for retry processing</li>
</ul>
<p>The API is organized in the following logical order:</p>
<ol>
<li>
<p><strong>Error Handle API</strong>: <code>getErrorString(error)</code> + <strong>Management APIs</strong></p>
<ul>
<li><strong>Function Code</strong>: 0b000 (Re-send request) - handles automatic retry mechanism</li>
<li><strong>FTDI-Style Management</strong>: Port management functions similar to FTDI RS485 drivers</li>
<li><strong>Buffer Management</strong>: Buffer status checking and overflow prevention</li>
<li><strong>Error Categorization</strong>: Handles COM port errors with recovery strategies</li>
<li><strong>Management Functions</strong>: <code>openPort()</code>, <code>closePort()</code>, <code>isPortOpen()</code>, <code>getBufferStatus()</code>, <code>clearBuffer()</code></li>
<li><strong>Buffer Flag Checking</strong>: Mandatory buffer status verification before transmission</li>
<li><strong>Retry Logic</strong>: Smart error recovery with transient vs. permanent error categorization</li>
<li><strong>Thread Pool Management</strong>: Dedicated thread pool for non-blocking operations</li>
</ul>
</li>
<li>
<p><strong>Master Broadcasting API</strong>: <code>configureSystemSettings(commandKey, value)</code> for S-series commands only</p>
<ul>
<li><strong>Function Code</strong>: 0b111 (Assign data) - for system-level broadcasting</li>
<li><strong>Buffer Check</strong>: Automatically checks uplink buffer flag before transmission</li>
<li><strong>Hardware Requirement</strong>: Only one slave device connected to prevent broadcast conflicts</li>
<li><strong>Address Assignment</strong>: Sets the slave address used by subsequent U-series commands</li>
<li><strong>Runtime Validation</strong>: Detects multiple slaves when only one is expected</li>
<li><strong>Acknowledgment</strong>: Mandatory acknowledgment mechanism with timeout and retry logic (function code 0b010)</li>
</ul>
</li>
<li>
<p><strong>Master Assign Data API</strong>: <code>configureUserSettings(commandKey, value)</code> + <code>modelDataOperation(address, data, isWrite, length)</code></p>
<ul>
<li><strong>Function Code</strong>: 0b111 (Assign data) - for targeted device configuration</li>
<li><strong>Buffer Check</strong>: Automatically checks uplink buffer flag before transmission</li>
<li><strong>U-series Commands</strong>: User configuration parameters using address from S001 command</li>
<li><strong>W-series Commands</strong>: AI model weights and bias data stored in FRAM memory</li>
<li><strong>Address Resolution</strong>: Uses slave address previously set by S001 or default address</li>
<li><strong>Acknowledgment</strong>: Expects acknowledgment responses with timeout and retry logic (function code 0b010)</li>
<li><strong>Memory Safety</strong>: Memory access validation for secure data operations</li>
</ul>
</li>
<li>
<p><strong>Master Request API</strong>: <code>requestData(dataKey)</code></p>
<ul>
<li><strong>Function Code</strong>: 0b110 (Request data) - for information queries</li>
<li><strong>Buffer Check</strong>: Automatically checks uplink buffer flag before transmission</li>
<li><strong>Non-blocking Design</strong>: Returns immediately for asynchronous handling</li>
<li><strong>A-series Commands</strong>: Application-related data queries and status requests</li>
<li><strong>Response Handling</strong>: Expects data responses with background processing and automatic retry (function code 0b001)</li>
<li><strong>Airborne Compatibility</strong>: Critical for multi-task airborne environments</li>
<li><strong>Performance Monitoring</strong>: Real-time latency tracking and performance metrics</li>
</ul>
</li>
<li>
<p><strong>Slave Response API</strong>: <code>receiveSlaveResponse(responseData, waitForData, timeout)</code></p>
<ul>
<li><strong>Function Codes</strong>: 0b010 (Response to Assign) and 0b001 (Response to Request)</li>
<li><strong>Buffer Management</strong>: Automatically checks downlink buffer flag before storing data</li>
<li><strong>FIFO Processing</strong>: Maintains strict First-In-First-Out ordering with per-slave queue management</li>
<li><strong>Response Types</strong>: Handles both acknowledgments (0b010) and data responses (0b001)</li>
<li><strong>Buffer Overflow</strong>: Configurable overflow policies when downlink buffer is full</li>
<li><strong>Data Ready Notification</strong>: Callback mechanism for asynchronous data availability</li>
<li><strong>Multi-Frame Response Handling</strong>: Automatically handles responses larger than 8 bytes</li>
<li><strong>Frame Sequencing</strong>: For responses smaller than 8 bytes, data is right-aligned</li>
<li><strong>Cross-Platform Validation</strong>: Ensures identical behavior across Windows and Linux platforms</li>
</ul>
</li>
</ol>
<p>This comprehensive API design allows users to focus on functional requirements without having to understand the underlying protocol details. The function code correspondence ensures that each API call is automatically routed to the correct protocol handling mechanism.</p>
<h3 id="35-management-apis-and-buffer-control">3.5 Management APIs and Buffer Control </h3>
<p><strong>FTDI-Style Management Functions</strong></p>
<p>Following industry-standard serial port interface patterns, the RS485 driver includes comprehensive management APIs similar to FTDI RS485 drivers:</p>
<p><strong>Port Management APIs:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Basic port operations (similar to FTDI FT_Open, FT_Close)</span>
ConnectionResult <span class="token function">openPort</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> portName<span class="token punctuation">)</span><span class="token punctuation">;</span>
ConnectionResult <span class="token function">closePort</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-bool">bool</span> <span class="token function">isPortOpen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>
PortResult <span class="token function">getPortInfo</span><span class="token punctuation">(</span>PortInfo<span class="token operator">&amp;</span> info<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Device enumeration (similar to FTDI FT_ListDevices)</span>
<span class="token keyword keyword-static">static</span> EnumerationResult <span class="token function">enumerateDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DeviceInfo<span class="token operator">&gt;</span><span class="token operator">&amp;</span> deviceList<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-static">static</span> DetectionResult <span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> detectedAddresses<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Buffer Management APIs (Critical for Data Integrity):</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Buffer status checking - MANDATORY before data transmission</span>
BufferResult <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>BufferStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">checkUplinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">checkDownlinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Buffer control operations</span>
BufferResult <span class="token function">clearBuffer</span><span class="token punctuation">(</span>BufferType bufferType <span class="token operator">=</span> BufferType<span class="token double-colon punctuation">::</span>BOTH<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">setBufferOverflowPolicy</span><span class="token punctuation">(</span>BufferOverflowPolicy policy<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">getBufferCapacity</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> uplinkFrames<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> downlinkFrames<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Buffer monitoring</span>
BufferResult <span class="token function">setBufferThreshold</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> thresholdPercent<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-void">void</span> <span class="token function">registerBufferThresholdCallback</span><span class="token punctuation">(</span>BufferThresholdCallbackFn callback<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Per-slave buffer management</span>
BufferResult <span class="token function">getPerSlaveBufferStatus</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">,</span> BufferStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Hardware Status APIs (Similar to FTDI FT_GetStatus):</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Hardware and communication status</span>
HardwareResult <span class="token function">getHardwareStatus</span><span class="token punctuation">(</span>HardwareStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
PerformanceResult <span class="token function">getPerformanceMetrics</span><span class="token punctuation">(</span>PerformanceMetrics<span class="token operator">&amp;</span> metrics<span class="token punctuation">)</span><span class="token punctuation">;</span>
ConfigResult <span class="token function">getBaudRate</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> currentBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>
LineResult <span class="token function">getLineStatus</span><span class="token punctuation">(</span>LineStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Critical Buffer Flag Checking:</strong></p>
<p>Every data transmission operation automatically performs buffer flag checking:</p>
<ol>
<li><strong>Buffer Validation</strong>: Driver checks uplink buffer flag to ensure space is available</li>
<li><strong>Storage Check</strong>: Driver checks downlink buffer flag to prevent overflow</li>
<li><strong>FIFO Guarantee</strong>: Strict First-In-First-Out ordering maintained</li>
<li><strong>Overflow Handling</strong>: Configurable policies when buffers reach capacity</li>
</ol>
<p><strong>Buffer Check Implementation:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Internal implementation - automatically called before each transmission</span>
BufferResult <span class="token function">checkBufferBeforeTransmission</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    BufferStatus status<span class="token punctuation">;</span>
    BufferResult result <span class="token operator">=</span> <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>status<span class="token punctuation">.</span>isUplinkFull<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Apply overflow policy</span>
        <span class="token keyword keyword-switch">switch</span> <span class="token punctuation">(</span>m_bufferOverflowPolicy<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-case">case</span> BufferOverflowPolicy<span class="token double-colon punctuation">::</span>DISCARD_OLDEST<span class="token operator">:</span>
                <span class="token function">clearOldestUplinkFrame</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-case">case</span> BufferOverflowPolicy<span class="token double-colon punctuation">::</span>DISCARD_NEWEST<span class="token operator">:</span>
                <span class="token keyword keyword-return">return</span> BufferResult<span class="token double-colon punctuation">::</span>INSUFFICIENT_BUFFER<span class="token punctuation">;</span>
            <span class="token keyword keyword-case">case</span> BufferOverflowPolicy<span class="token double-colon punctuation">::</span>TRIGGER_ERROR<span class="token operator">:</span>
                <span class="token keyword keyword-return">return</span> BufferResult<span class="token double-colon punctuation">::</span>INSUFFICIENT_BUFFER<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-return">return</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><p>This management API design follows industry-standard serial port communication patterns while providing the buffer management required for reliable RS485 communication.</p>
<h3 id="36-command-addressing-mechanism">3.6 Command Addressing Mechanism </h3>
<p><strong>Important Note on A-series and U-series Command Addressing:</strong></p>
<p>The RS485 driver implements an internal addressing mechanism for A-series and U-series commands:</p>
<ol>
<li>
<p><strong>A-series Commands (A001, A002, etc.)</strong>: When using <code>requestData(dataKey)</code> for A-series commands, the driver automatically uses:</p>
<ul>
<li>The slave address previously set by an S001 command via <code>configureSystemSettings</code></li>
<li>The default slave address (0x00) if no S001 command has been executed</li>
</ul>
</li>
<li>
<p><strong>U-series Commands (U001, U002, etc.)</strong>: When using <code>configureUserSettings(commandKey, value)</code> for U-series commands, the driver automatically uses:</p>
<ul>
<li>The slave address previously set by an S001 command via <code>configureSystemSettings</code></li>
<li>The default slave address (0x00) if no S001 command has been executed</li>
</ul>
</li>
<li>
<p>This design means that:</p>
<ul>
<li>You should typically set the slave address using S001 before sending A-series or U-series commands</li>
<li>All A-series and U-series commands will be sent to the same slave address until a new S001 command is executed</li>
<li>If you need to communicate with multiple slaves, you must change the slave address using S001 between each set of commands</li>
</ul>
</li>
<li>
<p>Sequence for proper addressing:</p>
<ul>
<li>First use <code>configureSystemSettings("S001", slaveAddress)</code> to set the target slave address</li>
<li><strong>Driver handles addressing</strong>: The driver automatically uses this address for all subsequent commands</li>
<li>Then use <code>configureUserSettings("Uxxxx", value)</code> for U-series commands to that slave</li>
<li>Then use <code>requestData("Axxx")</code> for A-series commands to that same slave</li>
<li><strong>Simplified API</strong>: No need to specify slave address in <code>receiveSlaveResponse()</code> calls</li>
<li>Repeat this sequence for each slave device that needs configuration or data requests</li>
</ul>
</li>
</ol>
<p><strong>Recommended Usage Pattern:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Step 1: Set the slave address (driver will use this internally)</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> targetSlaveAddress <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> targetSlaveAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Step 2: Driver automatically uses the configured address for all subsequent operations</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A001"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Step 3: For multiple slaves, change address and repeat</span>
targetSlaveAddress <span class="token operator">=</span> <span class="token number">6</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> targetSlaveAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// ... continue with new address</span>
</code></pre><h3 id="37-command-categories-and-definitions">3.7 Command Categories and Definitions </h3>
<p>The ZES protocol defines several categories of commands that can be exchanged between the master (PC) and slave (FPGA) devices:</p>
<h4 id="371-system-configuration-commands-s-series">3.7.1 System Configuration Commands (S-series) </h4>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Value Range</th>
<th>API Call Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>S001</strong></td>
<td>Set RS485 slave address</td>
<td>1-31</td>
<td><code>configureSystemSettings('S001', 5)</code></td>
</tr>
<tr>
<td><strong>S002</strong></td>
<td>Set baud rate</td>
<td>9600, 19200, 38400, 57600, 115200</td>
<td><code>configureSystemSettings('S002', 115200)</code></td>
</tr>
</tbody>
</table>
<p><strong>Important Notes on S-series Commands:</strong></p>
<ul>
<li><strong>S001 (Address Assignment)</strong>: All AI-SLDAP boards are initialized with default address 0x00 during manufacturing. To assign a user-designated address, connect the board alone (without other slaves) and use broadcasting to address 0x00. The FPGA will write the new address to FRAM for persistence across power cycles.
<ul>
<li><strong>Conflict Prevention</strong>: To prevent address conflicts, ensure only one slave device is connected to the RS485 bus during the S001 address assignment process. If multiple slaves respond to the S001 broadcast frame, the master should abort the assignment and alert the user to isolate a single slave.</li>
</ul>
</li>
<li><strong>S002 (Baud Rate Assignment)</strong>: Slave devices use default baud rate 9600 upon power-on before assignment. After receiving S002 command, the slave switches to the new baud rate and sends an acknowledgment frame at the new baud rate. The master waits for this acknowledgment before continuing communication at the new baud rate.
<ul>
<li><strong>Confirmation Mechanism</strong>: The slave acknowledges the baud rate change by sending a response frame (function code 0b010) at the newly assigned baud rate, which the master must receive before proceeding with further communication.</li>
</ul>
</li>
</ul>
<h4 id="372-user-configuration-commands-u-series">3.7.2 User Configuration Commands (U-series) </h4>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Value Range</th>
<th>API Call Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>U001</strong></td>
<td>Set SEL detection threshold</td>
<td>40-500 milliampere</td>
<td><code>configureUserSettings('U001', 250)</code></td>
</tr>
<tr>
<td><strong>U002</strong></td>
<td>Set SEL maximum amplitude threshold</td>
<td>1000-2000 milliampere</td>
<td><code>configureUserSettings('U002', 1500)</code></td>
</tr>
<tr>
<td><strong>U003</strong></td>
<td>Set number of SEL detections before power cycle</td>
<td>1-5</td>
<td><code>configureUserSettings('U003', 3)</code></td>
</tr>
<tr>
<td><strong>U004</strong></td>
<td>Set power cycle duration</td>
<td>200, 400, 600, 800, or 1000 milliseconds</td>
<td><code>configureUserSettings('U004', 600)</code></td>
</tr>
<tr>
<td><strong>U005</strong></td>
<td>Enable/disable GPIO input functions</td>
<td>See GPIO Value Packing below</td>
<td><code>configureUserSettings('U005', 0x100000000ULL)</code></td>
</tr>
<tr>
<td><strong>U006</strong></td>
<td>Enable/disable GPIO output functions</td>
<td>See GPIO Value Packing below</td>
<td><code>configureUserSettings('U006', 0x100000001ULL)</code></td>
</tr>
</tbody>
</table>
<p><strong>Configuration Persistence:</strong><br>
All U-series configuration settings are automatically saved to the FPGA board's FRAM (Ferroelectric Random Access Memory) upon successful execution. This ensures that:</p>
<ul>
<li><strong>Non-volatile Storage</strong>: Configuration settings persist across power cycles and system reboots</li>
<li><strong>One-time Setup</strong>: Users only need to configure the device once; settings are retained permanently</li>
<li><strong>Immediate Effect</strong>: Changes take effect immediately and are simultaneously saved to FRAM</li>
<li><strong>Reliability</strong>: FRAM technology provides high endurance and data retention without requiring external power</li>
</ul>
<p><strong>Reading Current Configuration:</strong><br>
To retrieve the current configuration values stored in FRAM, use the A005 command (see Section 1.4.3 Application Data Commands).</p>
<p><strong>GPIO Value Packing for U005/U006:</strong><br>
The <code>value</code> parameter for GPIO commands uses the following bit layout:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// GPIO Value Packing Format:</span>
<span class="token comment">// Lower 32 bits: Channel ID (0 or 1)</span>
<span class="token comment">// Upper 32 bits: Enable/Disable flag (0 = disable, 1 = enable)</span>

<span class="token keyword keyword-uint64_t">uint64_t</span> value <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>enable_flag<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">32</span><span class="token punctuation">)</span> <span class="token operator">|</span> channel_id<span class="token punctuation">;</span>

<span class="token comment">// Examples:</span>
<span class="token comment">// Enable GPIO input channel 0:  value = (1 &lt;&lt; 32) | 0 = 0x100000000ULL</span>
<span class="token comment">// Disable GPIO input channel 1: value = (0 &lt;&lt; 32) | 1 = 0x000000001ULL</span>
<span class="token comment">// Enable GPIO output channel 1:  value = (1 &lt;&lt; 32) | 1 = 0x100000001ULL</span>
</code></pre><h4 id="373-application-data-commands-a-series">3.7.3 Application Data Commands (A-series) </h4>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Response Data</th>
<th>API Call Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>A001</strong></td>
<td>Request SEL event log</td>
<td>JSON structure with event records</td>
<td><code>requestData('A001')</code></td>
</tr>
<tr>
<td><strong>A002</strong></td>
<td>Request device status</td>
<td>Status flags (16-bit)</td>
<td><code>requestData('A002')</code></td>
</tr>
<tr>
<td><strong>A003</strong></td>
<td>Request firmware version</td>
<td>Version string</td>
<td><code>requestData('A003')</code></td>
</tr>
<tr>
<td><strong>A004</strong></td>
<td>Request system statistics</td>
<td>JSON structure with statistics</td>
<td><code>requestData('A004')</code></td>
</tr>
<tr>
<td><strong>A005</strong></td>
<td>Request current configuration</td>
<td>JSON structure with all current settings</td>
<td><code>requestData('A005')</code></td>
</tr>
</tbody>
</table>
<p><strong>Example A001 Response Format:</strong></p>
<pre data-role="codeBlock" data-info="json" class="language-json json"><code><span class="token punctuation">{</span>
  <span class="token property">"events"</span><span class="token operator">:</span> <span class="token punctuation">[</span>
    <span class="token punctuation">{</span>
      <span class="token property">"id"</span><span class="token operator">:</span> <span class="token number">1</span><span class="token punctuation">,</span>
      <span class="token property">"timestamp"</span><span class="token operator">:</span> <span class="token string">"2024-12-15T14:30:22Z"</span><span class="token punctuation">,</span>
      <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"SEL_DETECTED"</span><span class="token punctuation">,</span>
      <span class="token property">"current"</span><span class="token operator">:</span> <span class="token number">320</span><span class="token punctuation">,</span>
      <span class="token property">"action"</span><span class="token operator">:</span> <span class="token string">"POWER_CYCLE"</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span>
      <span class="token property">"id"</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
      <span class="token property">"timestamp"</span><span class="token operator">:</span> <span class="token string">"2024-12-15T18:45:10Z"</span><span class="token punctuation">,</span>
      <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"SEL_DETECTED"</span><span class="token punctuation">,</span>
      <span class="token property">"current"</span><span class="token operator">:</span> <span class="token number">450</span><span class="token punctuation">,</span>
      <span class="token property">"action"</span><span class="token operator">:</span> <span class="token string">"POWER_CYCLE"</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">]</span><span class="token punctuation">,</span>
  <span class="token property">"total_count"</span><span class="token operator">:</span> <span class="token number">2</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>Example A004 Response Format:</strong></p>
<pre data-role="codeBlock" data-info="json" class="language-json json"><code><span class="token punctuation">{</span>
  <span class="token property">"statistics"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"total_runtime_hours"</span><span class="token operator">:</span> <span class="token number">720</span><span class="token punctuation">,</span>
    <span class="token property">"power_cycles"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token property">"total"</span><span class="token operator">:</span> <span class="token number">15</span><span class="token punctuation">,</span>
      <span class="token property">"last_24h"</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
      <span class="token property">"last_7d"</span><span class="token operator">:</span> <span class="token number">8</span><span class="token punctuation">,</span>
      <span class="token property">"last_30d"</span><span class="token operator">:</span> <span class="token number">15</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token property">"sel_events"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token property">"total"</span><span class="token operator">:</span> <span class="token number">12</span><span class="token punctuation">,</span>
      <span class="token property">"last_24h"</span><span class="token operator">:</span> <span class="token number">1</span><span class="token punctuation">,</span>
      <span class="token property">"last_7d"</span><span class="token operator">:</span> <span class="token number">6</span><span class="token punctuation">,</span>
      <span class="token property">"last_30d"</span><span class="token operator">:</span> <span class="token number">12</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token property">"average_current"</span><span class="token operator">:</span> <span class="token number">180</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token property">"timestamp"</span><span class="token operator">:</span> <span class="token string">"2024-12-16T09:30:00Z"</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>Example A005 Response Format:</strong></p>
<pre data-role="codeBlock" data-info="json" class="language-json json"><code><span class="token punctuation">{</span>
  <span class="token property">"configuration"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"system_settings"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token property">"slave_address"</span><span class="token operator">:</span> <span class="token number">5</span><span class="token punctuation">,</span>
      <span class="token property">"baud_rate"</span><span class="token operator">:</span> <span class="token number">115200</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token property">"user_settings"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token property">"sel_detection_threshold"</span><span class="token operator">:</span> <span class="token number">250</span><span class="token punctuation">,</span>
      <span class="token property">"sel_max_amplitude_threshold"</span><span class="token operator">:</span> <span class="token number">1500</span><span class="token punctuation">,</span>
      <span class="token property">"sel_detection_count"</span><span class="token operator">:</span> <span class="token number">3</span><span class="token punctuation">,</span>
      <span class="token property">"power_cycle_duration"</span><span class="token operator">:</span> <span class="token number">600</span><span class="token punctuation">,</span>
      <span class="token property">"gpio_input_channels"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token property">"channel_0"</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
        <span class="token property">"channel_1"</span><span class="token operator">:</span> <span class="token boolean">false</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token property">"gpio_output_channels"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token property">"channel_0"</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>
        <span class="token property">"channel_1"</span><span class="token operator">:</span> <span class="token boolean">true</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token property">"last_updated"</span><span class="token operator">:</span> <span class="token string">"2024-12-16T10:15:30Z"</span><span class="token punctuation">,</span>
    <span class="token property">"fram_status"</span><span class="token operator">:</span> <span class="token string">"healthy"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h4 id="374-weight-and-bias-data-commands-w-series">3.7.4 Weight and Bias Data Commands (W-series) </h4>
<table>
<thead>
<tr>
<th>Operation</th>
<th>Description</th>
<th>Parameters</th>
<th>API Call Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>W001</strong></td>
<td>Write model data to FRAM</td>
<td>Memory address, data bytes</td>
<td><code>modelDataOperation(0x1000, data, true, 8)</code></td>
</tr>
<tr>
<td><strong>W002</strong></td>
<td>Read model data from FRAM</td>
<td>Memory address, length</td>
<td><code>modelDataOperation(0x1000, data, false, 8)</code></td>
</tr>
</tbody>
</table>
<h3 id="38-implementation-approach">3.8 Implementation Approach </h3>
<ul>
<li><strong>PC Side</strong>: Single Windows executable application incorporating Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality</li>
<li><strong>FPGA Side</strong>: VHDL/Verilog implementation of the slave device functionality (ZM-AISL-01 boards)</li>
<li><strong>Data Link Layer Implementation</strong>: Native Windows driver using WDK implementing ZES protocol with embedded FTDI VCP driver capabilities</li>
<li><strong>Hardware Interface</strong>: Built-in support for USB-RS485-WE-1800-BT FTDI converter without requiring separate driver installation</li>
<li><strong>Buffer Management</strong>: Driver-managed payload buffers (5 uplink × 12 bytes + 10 downlink × 12 bytes) with FIFO integrity</li>
<li><strong>API Design Philosophy</strong>: High abstraction level with type-safe data handling and cross-platform compatibility</li>
<li><strong>Deployment Model</strong>: Single executable deployment eliminating driver installation complexity</li>
</ul>
<h3 id="39-data-structure-design-decisions">3.9 Data Structure Design Decisions </h3>
<p><strong>Fixed-Size Arrays vs. Dynamic Vectors</strong></p>
<p>The RS485 driver API uses <strong>fixed-size arrays</strong> instead of <code>std::vector</code> for payload data handling. This design decision provides several critical advantages:</p>
<p><strong>Why Fixed-Size Arrays (uint8_t data[12]):</strong></p>
<ol>
<li><strong>Memory Predictability</strong>: No dynamic allocation overhead or fragmentation</li>
<li><strong>Real-Time Performance</strong>: Deterministic memory access patterns for airborne systems</li>
<li><strong>Cross-Platform Compatibility</strong>: Works identically across different C++ implementations</li>
<li><strong>Cache Efficiency</strong>: Fixed-size data structures improve CPU cache utilization</li>
<li><strong>Stack Allocation</strong>: Data can be allocated on stack, avoiding heap management</li>
<li><strong>DeviceIoControl Compatibility</strong>: Kernel drivers work better with fixed-size buffers</li>
</ol>
<p><strong>Why Not std::vector:</strong></p>
<ol>
<li><strong>Dynamic Allocation</strong>: Unpredictable memory allocation can cause real-time issues</li>
<li><strong>Exception Safety</strong>: Vector operations can throw exceptions in low-memory conditions</li>
<li><strong>Memory Overhead</strong>: Vector has additional metadata overhead (size, capacity, etc.)</li>
<li><strong>Kernel Interface</strong>: DeviceIoControl works more efficiently with fixed-size buffers</li>
</ol>
<p><strong>Protocol Justification:</strong><br>
The ZES protocol defines a fixed 12-byte payload size, making fixed-size arrays the natural choice:</p>
<ul>
<li><strong>Frame Structure</strong>: Header(1) + ID(1) + <strong>Payload(12)</strong> + CRC(1) + Trailer(1) = 16 bytes</li>
<li><strong>Payload Content</strong>: Key(4 bytes) + Value(8 bytes) = 12 bytes total</li>
<li><strong>No Variable Length</strong>: Protocol does not support variable-length payloads</li>
</ul>
<p><strong>Example Comparison:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Fixed-size array approach (RECOMMENDED)</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Vector approach (NOT USED - for comparison only)</span>
std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span> responseData<span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="310-protocol-layer-separation-complete-frame-vs-payload-data">3.10 Protocol Layer Separation: Complete Frame vs Payload Data </h3>
<p><strong>Critical Design Concept: Two-Layer Data Processing</strong></p>
<p>This section addresses a common confusion about data handling in the RS485 driver: the difference between complete frame processing and payload data handling.</p>
<p><strong>Layer 1: Driver-Level Frame Processing (16 bytes)</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-struct">struct</span> <span class="token class-name">RS485Frame</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> header<span class="token punctuation">;</span>        <span class="token comment">// 0xAA - Frame start marker</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> id_byte<span class="token punctuation">;</span>       <span class="token comment">// Function code (3 bits) + Device address (5 bits)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>   <span class="token comment">// Key (4 bytes) + Value (8 bytes) - APPLICATION DATA</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> crc8<span class="token punctuation">;</span>          <span class="token comment">// CRC8 checksum for error detection</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> trailer<span class="token punctuation">;</span>       <span class="token comment">// 0x0D - Frame end marker</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>  <span class="token comment">// Total: 16 bytes</span>
</code></pre><p><strong>Driver Responsibilities (Hidden from User):</strong></p>
<ol>
<li><strong>Frame Synchronization</strong>: Detect Header (0xAA) and Trailer (0x0D)</li>
<li><strong>Error Detection</strong>: Verify CRC8 checksum</li>
<li><strong>ID Byte Processing</strong>: Extract and process function code and device address</li>
<li><strong>Function Code Routing</strong>: Route frames to appropriate API categories</li>
<li><strong>Address Filtering</strong>: Ensure responses match expected device addresses</li>
</ol>
<p><strong>Layer 2: User-Level Payload Processing (12 bytes)</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// User application interface - only handles meaningful data</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// Key (4 bytes) + Value (8 bytes)</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// responseData structure:</span>
<span class="token comment">// Bytes 0-3: Key (4-byte ASCII string, null-terminated if &lt; 4 chars)</span>
<span class="token comment">// Bytes 4-11: Value (8-byte binary data in little-endian format)</span>
</code></pre><p><strong>Detailed Data Storage Format:</strong></p>
<p><strong>Key Storage (Bytes 0-3) - ASCII String Format:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Example key storage patterns:</span>
<span class="token comment">// "A001" → [0x41, 0x30, 0x30, 0x31] (4 ASCII characters)</span>
<span class="token comment">// "U1"   → [0x55, 0x31, 0x00, 0x00] (2 chars + 2 null bytes)</span>
<span class="token comment">// "S002" → [0x53, 0x30, 0x30, 0x32] (4 ASCII characters)</span>

<span class="token comment">// Key extraction helper:</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">extractKey</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-char">char</span> keyBuffer<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token number">0</span><span class="token punctuation">}</span><span class="token punctuation">;</span>  <span class="token comment">// 4 chars + null terminator</span>
    <span class="token function">memcpy</span><span class="token punctuation">(</span>keyBuffer<span class="token punctuation">,</span> payload<span class="token punctuation">,</span> <span class="token number">4</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> std<span class="token double-colon punctuation">::</span><span class="token function">string</span><span class="token punctuation">(</span>keyBuffer<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>Value Storage (Bytes 4-11) - Binary Little-Endian Format:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Integer value storage (32-bit, uses bytes 4-7, bytes 8-11 set to zero):</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> intValue <span class="token operator">=</span> <span class="token number">1500</span><span class="token punctuation">;</span>  <span class="token comment">// SEL threshold = 1500 mA</span>
payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>intValue <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>   <span class="token comment">// LSB</span>
payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>intValue <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>intValue <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>intValue <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>  <span class="token comment">// MSB</span>
payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token number">0x00</span><span class="token punctuation">;</span>  <span class="token comment">// Upper 4 bytes zero</span>

<span class="token comment">// Float value storage (IEEE 754 single-precision, uses bytes 4-7):</span>
<span class="token keyword keyword-float">float</span> floatValue <span class="token operator">=</span> <span class="token number">3.14159f</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> floatBits <span class="token operator">=</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>floatValue<span class="token punctuation">)</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>floatBits <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>   <span class="token comment">// LSB</span>
payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>floatBits <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>floatBits <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>floatBits <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>  <span class="token comment">// MSB</span>
payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token number">0x00</span><span class="token punctuation">;</span>  <span class="token comment">// Upper 4 bytes zero</span>

<span class="token comment">// Double value storage (IEEE 754 double-precision, uses all bytes 4-11):</span>
<span class="token keyword keyword-double">double</span> doubleValue <span class="token operator">=</span> <span class="token number">3.141592653589793</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint64_t">uint64_t</span> doubleBits <span class="token operator">=</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>doubleValue<span class="token punctuation">)</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>   <span class="token comment">// LSB</span>
payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">32</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">40</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">48</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">56</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span> <span class="token comment">// MSB</span>

<span class="token comment">// Dual integer storage (two 32-bit integers):</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> value1 <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>     <span class="token comment">// GPIO channel</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> value2 <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>     <span class="token comment">// Enable flag</span>
<span class="token comment">// First integer in bytes 4-7:</span>
payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
<span class="token comment">// Second integer in bytes 8-11:</span>
payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
</code></pre><p><strong>User Responsibilities (Simplified):</strong></p>
<ol>
<li><strong>Key Interpretation</strong>: Understand command identifiers ("A001", "U001", etc.)</li>
<li><strong>Value Processing</strong>: Handle 8-byte data values (integers, floats, structured data)</li>
<li><strong>Application Logic</strong>: Implement business logic based on received data</li>
</ol>
<p><strong>Data Extraction Helper Functions:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Helper class for consistent data extraction from 12-byte payload</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">PayloadDataExtractor</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Extract key from bytes 0-3</span>
    <span class="token keyword keyword-static">static</span> std<span class="token double-colon punctuation">::</span>string <span class="token function">extractKey</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-char">char</span> keyBuffer<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token number">0</span><span class="token punctuation">}</span><span class="token punctuation">;</span>  <span class="token comment">// 4 chars + null terminator</span>
        <span class="token function">memcpy</span><span class="token punctuation">(</span>keyBuffer<span class="token punctuation">,</span> payload<span class="token punctuation">,</span> <span class="token number">4</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> std<span class="token double-colon punctuation">::</span><span class="token function">string</span><span class="token punctuation">(</span>keyBuffer<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Extract 32-bit integer from bytes 4-7 (little-endian)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-uint32_t">uint32_t</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">|</span>
               <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">|</span>
               <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">|</span>
               <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">24</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Extract IEEE 754 single-precision float from bytes 4-7</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-float">float</span> <span class="token function">extractFloat</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> floatBits <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-float">float</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>floatBits<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Extract IEEE 754 double-precision float from bytes 4-11</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-double">double</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint64_t">uint64_t</span> doubleBits <span class="token operator">=</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">32</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">40</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">48</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">56</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>doubleBits<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Extract dual 32-bit integers from bytes 4-7 and 8-11</span>
    <span class="token keyword keyword-static">static</span> std<span class="token double-colon punctuation">::</span>pair<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span> <span class="token function">extractDualIntegers</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> value1 <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Bytes 4-7</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> value2 <span class="token operator">=</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">|</span>
            <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">24</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Bytes 8-11</span>
        <span class="token keyword keyword-return">return</span> std<span class="token double-colon punctuation">::</span><span class="token function">make_pair</span><span class="token punctuation">(</span>value1<span class="token punctuation">,</span> value2<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Store key into bytes 0-3 (ASCII string, null-padded)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">storeKey</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> key<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token function">memset</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token number">4</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Clear first 4 bytes</span>
        size_t copyLen <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token function">min</span><span class="token punctuation">(</span>key<span class="token punctuation">.</span><span class="token function">length</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>size_t<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token number">4</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">memcpy</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> key<span class="token punctuation">.</span><span class="token function">c_str</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> copyLen<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Store 32-bit integer into bytes 4-7 (little-endian, zero upper bytes)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">storeInteger</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token number">0x00</span><span class="token punctuation">;</span>  <span class="token comment">// Clear upper bytes</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Store IEEE 754 float into bytes 4-7 (zero upper bytes)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">storeFloat</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">,</span> <span class="token keyword keyword-float">float</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> floatBits <span class="token operator">=</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">storeInteger</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> floatBits<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Store IEEE 754 double into bytes 4-11</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">storeDouble</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">,</span> <span class="token keyword keyword-double">double</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint64_t">uint64_t</span> doubleBits <span class="token operator">=</span> <span class="token operator">*</span><span class="token generic-function"><span class="token function">reinterpret_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">32</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">40</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">48</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>doubleBits <span class="token operator">&gt;&gt;</span> <span class="token number">56</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Store dual 32-bit integers into bytes 4-7 and 8-11</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-void">void</span> <span class="token function">storeDualIntegers</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> payload<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> value1<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> value2<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Store first integer in bytes 4-7</span>
        payload<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">5</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">6</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">7</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value1 <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        <span class="token comment">// Store second integer in bytes 8-11</span>
        payload<span class="token punctuation">[</span><span class="token number">8</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">9</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
        payload<span class="token punctuation">[</span><span class="token number">11</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">(</span>value2 <span class="token operator">&gt;&gt;</span> <span class="token number">24</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xFF</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>ID Byte Processing Example (Driver Internal):</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// This processing happens automatically in the driver - users never see this code</span>
<span class="token keyword keyword-void">void</span> <span class="token function">ProcessIncomingFrame</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Extract function code from ID byte (bits 7-5)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> functionCode <span class="token operator">=</span> <span class="token punctuation">(</span>frame<span class="token punctuation">.</span>id_byte <span class="token operator">&gt;&gt;</span> <span class="token number">5</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0x07</span><span class="token punctuation">;</span>

    <span class="token comment">// Extract device address from ID byte (bits 4-0)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> deviceAddress <span class="token operator">=</span> frame<span class="token punctuation">.</span>id_byte <span class="token operator">&amp;</span> <span class="token number">0x1F</span><span class="token punctuation">;</span>

    <span class="token comment">// Automatic routing based on function code</span>
    <span class="token keyword keyword-switch">switch</span> <span class="token punctuation">(</span>functionCode<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-case">case</span> <span class="token number">0b111</span><span class="token operator">:</span>  <span class="token comment">// Assign data - Routes to Broadcasting/Assign Data API</span>
            <span class="token function">ProcessAssignDataFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">.</span>payload<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-case">case</span> <span class="token number">0b110</span><span class="token operator">:</span>  <span class="token comment">// Request data - Routes to Master Request API</span>
            <span class="token function">ProcessRequestDataFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">.</span>payload<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-case">case</span> <span class="token number">0b010</span><span class="token operator">:</span>  <span class="token comment">// Response to Assign - Routes to Slave Response API</span>
            <span class="token function">ProcessResponseAssignFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">.</span>payload<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-case">case</span> <span class="token number">0b001</span><span class="token operator">:</span>  <span class="token comment">// Response to Request - Routes to Slave Response API</span>
            <span class="token function">ProcessResponseRequestFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">.</span>payload<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-case">case</span> <span class="token number">0b000</span><span class="token operator">:</span>  <span class="token comment">// Re-send request - Routes to Error Handle API</span>
            <span class="token function">ProcessResendRequestFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">.</span>payload<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Only the 12-byte payload is passed to user application</span>
    <span class="token comment">// All protocol overhead (Header, ID, CRC, Trailer) is handled internally</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>Key Benefits of This Separation:</strong></p>
<ol>
<li><strong>User Simplicity</strong>: Applications only handle meaningful 12-byte data</li>
<li><strong>Protocol Reliability</strong>: Driver ensures all frames are valid before passing data to user</li>
<li><strong>Automatic Routing</strong>: Function codes automatically route to correct API categories</li>
<li><strong>Error Isolation</strong>: Protocol errors are handled at driver level, not user level</li>
<li><strong>Address Management</strong>: Driver automatically filters responses by device address</li>
</ol>
<p><strong>Practical Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Example 1: Receiving A001 response (SEL event log)</span>
<span class="token comment">// Note: Driver automatically uses the address configured via S001</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"A001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// For A001, the 8-byte value contains JSON data pointer or structured data</span>
        <span class="token keyword keyword-uint64_t">uint64_t</span> jsonDataPointer <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractDouble</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token comment">// Process JSON data...</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// Example 2: Receiving U001 configuration confirmation</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"U001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// U001 response contains the configured threshold value</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> threshold <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractInteger</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"SEL threshold set to: "</span> <span class="token operator">&lt;&lt;</span> threshold <span class="token operator">&lt;&lt;</span> <span class="token string">" mA"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// Example 3: Receiving U005 GPIO configuration confirmation</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"U005"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// U005 response contains channel and enable flag</span>
        <span class="token keyword keyword-auto">auto</span> <span class="token punctuation">[</span>channel<span class="token punctuation">,</span> enableFlag<span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">extractDualIntegers</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"GPIO input channel "</span> <span class="token operator">&lt;&lt;</span> channel
                  <span class="token operator">&lt;&lt;</span> <span class="token punctuation">(</span>enableFlag <span class="token operator">?</span> <span class="token string">" enabled"</span> <span class="token operator">:</span> <span class="token string">" disabled"</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// Example 4: Sending configuration with proper payload formatting</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> configPayload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
<span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeKey</span><span class="token punctuation">(</span>configPayload<span class="token punctuation">,</span> <span class="token string">"U001"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeInteger</span><span class="token punctuation">(</span>configPayload<span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 250 mA threshold</span>

ConfigurationResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span>configPayload<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Data Type Validation and Safety:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Type-safe wrapper functions for common operations</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">RS485DataHandler</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Safe integer configuration with range validation</span>
    <span class="token keyword keyword-static">static</span> ConfigurationResult <span class="token function">configureIntegerSetting</span><span class="token punctuation">(</span>
        RS485Driver<span class="token operator">&amp;</span> driver<span class="token punctuation">,</span>
        <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> key<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> value<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> minValue<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> maxValue<span class="token punctuation">)</span> <span class="token punctuation">{</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>value <span class="token operator">&lt;</span> minValue <span class="token operator">||</span> value <span class="token operator">&gt;</span> maxValue<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>INVALID_PARAMETER<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeKey</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> key<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeInteger</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-return">return</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Safe float configuration with validation</span>
    <span class="token keyword keyword-static">static</span> ConfigurationResult <span class="token function">configureFloatSetting</span><span class="token punctuation">(</span>
        RS485Driver<span class="token operator">&amp;</span> driver<span class="token punctuation">,</span>
        <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> key<span class="token punctuation">,</span>
        <span class="token keyword keyword-float">float</span> value<span class="token punctuation">,</span>
        <span class="token keyword keyword-float">float</span> minValue<span class="token punctuation">,</span>
        <span class="token keyword keyword-float">float</span> maxValue<span class="token punctuation">)</span> <span class="token punctuation">{</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>value <span class="token operator">&lt;</span> minValue <span class="token operator">||</span> value <span class="token operator">&gt;</span> maxValue <span class="token operator">||</span>
            <span class="token operator">!</span>std<span class="token double-colon punctuation">::</span><span class="token function">isfinite</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span> <span class="token operator">||</span> std<span class="token double-colon punctuation">::</span><span class="token function">isnan</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>INVALID_PARAMETER<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeKey</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> key<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeFloat</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-return">return</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Safe dual integer configuration (for GPIO commands)</span>
    <span class="token keyword keyword-static">static</span> ConfigurationResult <span class="token function">configureDualIntegerSetting</span><span class="token punctuation">(</span>
        RS485Driver<span class="token operator">&amp;</span> driver<span class="token punctuation">,</span>
        <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> key<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> value1<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> value2<span class="token punctuation">)</span> <span class="token punctuation">{</span>

        <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeKey</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> key<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token class-name">PayloadDataExtractor</span><span class="token double-colon punctuation">::</span><span class="token function">storeDualIntegers</span><span class="token punctuation">(</span>payload<span class="token punctuation">,</span> value1<span class="token punctuation">,</span> value2<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-return">return</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span>payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Usage examples with type safety:</span>
<span class="token comment">// Configure SEL threshold (250 mA, valid range 40-500)</span>
<span class="token class-name">RS485DataHandler</span><span class="token double-colon punctuation">::</span><span class="token function">configureIntegerSetting</span><span class="token punctuation">(</span>driver<span class="token punctuation">,</span> <span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">,</span> <span class="token number">500</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Configure GPIO input channel 0 enable</span>
<span class="token class-name">RS485DataHandler</span><span class="token double-colon punctuation">::</span><span class="token function">configureDualIntegerSetting</span><span class="token punctuation">(</span>driver<span class="token punctuation">,</span> <span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Channel 0, Enable</span>

<span class="token comment">// Configure power cycle duration (600 ms, valid values: 200,400,600,800,1000)</span>
<span class="token class-name">RS485DataHandler</span><span class="token double-colon punctuation">::</span><span class="token function">configureIntegerSetting</span><span class="token punctuation">(</span>driver<span class="token punctuation">,</span> <span class="token string">"U004"</span><span class="token punctuation">,</span> <span class="token number">600</span><span class="token punctuation">,</span> <span class="token number">200</span><span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Common Misconception Clarification:</strong></p>
<ul>
<li><strong>Misconception</strong>: "Users need to process the complete 16-byte frame"</li>
<li><strong>Reality</strong>: Users only process the 12-byte payload; driver handles all protocol details</li>
<li><strong>Misconception</strong>: "Users need to manually parse ID byte for function codes"</li>
<li><strong>Reality</strong>: Driver automatically routes frames based on function codes in ID byte</li>
<li><strong>Misconception</strong>: "String keys are stored as null-terminated C strings"</li>
<li><strong>Reality</strong>: Keys are stored as fixed 4-byte ASCII data, null-padded if shorter than 4 characters</li>
<li><strong>Misconception</strong>: "Different data types require different payload sizes"</li>
<li><strong>Reality</strong>: All payloads are exactly 12 bytes; data types are differentiated by interpretation of the 8-byte value portion</li>
</ul>
<h3 id="311-api-data-format-specification">3.11 API Data Format Specification </h3>
<p><strong>Critical Design Principle: Universal Cross-Platform Data Format</strong></p>
<p>The RS485 driver API uses a universal data format that ensures compatibility across different systems (Windows, Linux), programming languages (C++, Python, Java, C#), and architectures (x86, x64, ARM). Users only need to provide raw data values - the driver automatically handles all data type conversion and formatting internally.</p>
<p><strong>User-Friendly Data Input:</strong></p>
<ul>
<li><strong>No Type Specification Required</strong>: Users input raw integer or floating-point values directly</li>
<li><strong>Automatic Format Conversion</strong>: Driver internally converts user data to the correct binary format</li>
<li><strong>Type-Safe Processing</strong>: Compile-time type safety prevents common programming errors</li>
<li><strong>Transparent Processing</strong>: Users don't need to understand binary encoding, endianness, or payload structure</li>
<li><strong>Cross-Platform Consistency</strong>: Same user input produces identical results across all platforms</li>
</ul>
<p><strong>API Parameter Format (Internal - Handled Automatically):</strong></p>
<ul>
<li><strong>Command Key</strong>: 4-byte ASCII string identifier stored in bytes 0-3 (e.g., "S001", "U001", "A001")</li>
<li><strong>Payload Data</strong>: 8-byte binary value stored in bytes 4-11 with little-endian byte ordering</li>
</ul>
<p><strong>Type-Safe User Interface Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// User simply provides the raw value</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>        <span class="token comment">// Integer: 250 mA threshold</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U004"</span><span class="token punctuation">,</span> <span class="token number">600</span><span class="token punctuation">)</span><span class="token punctuation">;</span>        <span class="token comment">// Integer: 600 ms duration</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"W001"</span><span class="token punctuation">,</span> <span class="token number">3.14159f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Float: 3.14159 weight value</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Channel=0, Enable=1</span>

<span class="token comment">// Driver processing includes:</span>
<span class="token comment">// 1. Compile-time type safety</span>
<span class="token comment">// 2. Runtime validation</span>
<span class="token comment">// 3. Automatic type detection</span>
<span class="token comment">// 4. Binary format conversion</span>
<span class="token comment">// 5. Cross-platform endianness handling</span>
</code></pre><p><strong>Payload Data Format Guidelines:</strong></p>
<ol>
<li><strong>Integer Values</strong>: Uses bytes 4-7 in little-endian format, bytes 8-11 set to zero</li>
<li><strong>Dual Integer Values</strong>: First integer in bytes 4-7, second integer in bytes 8-11 (both little-endian)</li>
<li><strong>Floating-Point Values</strong>: IEEE 754 format - single precision in bytes 4-7, double precision in bytes 4-11</li>
<li><strong>Fixed-Point Values</strong>: 32-bit integer in bytes 4-7 with documented scale factor</li>
</ol>
<p><strong>Cross-Platform Compatibility:</strong></p>
<ul>
<li><strong>Byte Order</strong>: All multi-byte values use little-endian format</li>
<li><strong>Alignment</strong>: No special alignment requirements</li>
<li><strong>Padding</strong>: Unused bytes set to zero</li>
<li><strong>Type Safety</strong>: API functions handle conversion from native types to wire format</li>
<li><strong>Language Independence</strong>: Format works identically across programming languages</li>
<li><strong>Architecture Independence</strong>: Same format on all processor architectures</li>
</ul>
<p><strong>Automatic Data Type Conversion Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Users provide raw values - driver handles conversion automatically</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>        <span class="token comment">// SEL threshold: 250 mA</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"W001"</span><span class="token punctuation">,</span> <span class="token number">3.14159f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Weight value: 3.14159</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Channel=0, Enable=1</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> <span class="token number">5</span><span class="token punctuation">)</span><span class="token punctuation">;</span>        <span class="token comment">// Slave address: 5</span>

<span class="token comment">// Driver automatically handles data type conversion and formatting</span>
</code></pre><p><strong>User-Friendly API Design Summary:</strong></p>
<p>The key principle of this RS485 driver API is <strong>simplicity for end users</strong>. Users interact with the driver using natural, intuitive function calls without needing to understand protocol details, data encoding, or cross-platform compatibility issues.</p>
<h3 id="312-windows-driver-kit-wdk-integration-and-application-architecture">3.12 Windows Driver Kit (WDK) Integration and Application Architecture </h3>
<p><strong>Application Framework Selection:</strong><br>
The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) within a single executable, providing simplified deployment, system stability, and built-in FTDI VCP driver functionality.</p>
<p><strong>Key WDK Components Integrated:</strong></p>
<ul>
<li><strong>UMDF 2.0</strong>: User-Mode Driver Framework</li>
<li><strong>WDF I/O Queues</strong>: Managing communication requests and asynchronous operations</li>
<li><strong>WDF Device Objects</strong>: Representing the RS485 device interface</li>
<li><strong>WDF Memory Objects</strong>: Efficient buffer management and data transfer</li>
<li><strong>FTDI VCP Integration</strong>: Embedded FTDI Virtual COM Port driver functionality</li>
</ul>
<p><strong>Application Deployment Model:</strong></p>
<ul>
<li><strong>Single Executable</strong>: Complete solution packaged as RS485_Communication_Application.exe</li>
<li><strong>No Driver Installation</strong>: Application handles all driver functionality internally</li>
<li><strong>Plug and Play Support</strong>: Automatic detection and configuration of USB-RS485 converters</li>
<li><strong>Windows Compatibility</strong>: Full compatibility with Windows 10/11</li>
</ul>
<h2 id="2-architecture-overview">2. Architecture Overview </h2>
<h3 id="20-integrated-application-architecture">2.0 Integrated Application Architecture </h3>
<p>The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) with embedded FTDI VCP driver functionality, providing a complete RS485 communication solution in a single executable.</p>
<p><strong>Integrated Application Architecture:</strong></p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>┌─────────────────────────────────────┐
│   RS485_Communication_Application   │
│              (.exe)                 │
├─────────────────────────────────────┤
│        High-Level API Layer         │
│  • configureSystemSettings()        │
│  • configureUserSettings()          │
│  • requestData()                    │
│  • receiveSlaveResponse()           │
│  • modelDataOperation()             │
├─────────────────────────────────────┤
│   Integrated UMDF 2.0 Framework     │
│  ┌─────────────────────────────────┐│
│  │   Driver-Managed Payload Buffers││
│  │  ┌─────────────┬─────────────┐  ││
│  │  │ Uplink (5)  │Downlink(10) │  ││
│  │  │ 5×12 bytes  │ 10×12 bytes │  ││
│  │  │ = 60 bytes  │ = 120 bytes │  ││
│  │  └─────────────┴─────────────┘  ││
│  │                                 ││
│  │   ZES Protocol Processing       ││
│  │   • Frame packing/unpacking     ││
│  │   • CRC8 calculation            ││
│  │   • Function code routing       ││
│  │   • Error handling &amp; retry      ││
│  └─────────────────────────────────┘│
├─────────────────────────────────────┤
│    Embedded FTDI VCP Functionality  │
│    (Integrated USB-Serial Driver)   │
├─────────────────────────────────────┤
│      Windows USB Stack              │
├─────────────────────────────────────┤
│   USB-RS485-WE-1800-BT Converter    │
├─────────────────────────────────────┤
│         RS485 Bus                   │
└─────────────────────────────────────┘
</code></pre><p><strong>Application Component Roles:</strong></p>
<ol>
<li>
<p><strong>Embedded FTDI VCP Functionality</strong> (Lower Layer):</p>
<ul>
<li><strong>Role</strong>: Integrated USB-to-Serial Driver</li>
<li><strong>Responsibility</strong>: USB-to-serial conversion and basic serial communication</li>
<li><strong>Implementation</strong>: Built-in FTDI VCP driver functionality</li>
</ul>
</li>
<li>
<p><strong>ZES Protocol Processing Layer</strong> (Upper Layer):</p>
<ul>
<li><strong>Role</strong>: Protocol Handler – Implements ZES proprietary data link layer protocol</li>
<li><strong>Responsibility</strong>: RS485 protocol handling, buffer management, frame processing, function code routing</li>
<li><strong>Framework</strong>: UMDF 2.0 integrated within application</li>
<li><strong>Interface</strong>: High-level API functions for user applications</li>
</ul>
</li>
</ol>
<h3 id="21-driver-managed-buffer-system">2.1 Driver-Managed Buffer System </h3>
<p><strong>Buffer Architecture:</strong><br>
The driver implements a sophisticated buffer management system to handle the asynchronous nature of RS485 communication:</p>
<p><strong>Uplink Buffer (PC to Device) - Transmitter Buffer:</strong></p>
<ul>
<li><strong>Capacity</strong>: 5 payload slots × 12 bytes = 60 bytes total</li>
<li><strong>Purpose</strong>: Queue outgoing 12-byte payload data from PC to slave devices</li>
<li><strong>Management</strong>: FIFO (First-In-First-Out) queue managed by the driver</li>
<li><strong>Overflow Prevention</strong>: Mandatory buffer flag checking before each transmission</li>
<li><strong>API Behavior</strong>: When buffer is full, API functions return BUFFER_FULL error to prevent data loss</li>
<li><strong>Frame-by-Frame Transmission</strong>: Data is sent one frame at a time with buffer availability check</li>
</ul>
<p><strong>Downlink Buffer (Device to PC) - Receiver Buffer:</strong></p>
<ul>
<li><strong>Capacity</strong>: 10 payload slots × 12 bytes = 120 bytes total</li>
<li><strong>Purpose</strong>: Store incoming 12-byte payload data from slave devices</li>
<li><strong>Management</strong>: FIFO queue with per-slave address organization</li>
<li><strong>Data Ready Notification</strong>: Non-blocking check mechanism for data availability</li>
<li><strong>Buffer Flag</strong>: Monitored to prevent overflow - checked before storing received payload data</li>
</ul>
<p><strong>Frame Structure (16 bytes per frame):</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-struct">struct</span> <span class="token class-name">RS485Frame</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> header<span class="token punctuation">;</span>        <span class="token comment">// 0xAA (1 byte)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> id_byte<span class="token punctuation">;</span>       <span class="token comment">// Function code + device address (1 byte)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> payload<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>   <span class="token comment">// Key (4 bytes) + Value (8 bytes) - CORE PROTOCOL DATA</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> crc8<span class="token punctuation">;</span>          <span class="token comment">// CRC checksum (1 byte)</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> trailer<span class="token punctuation">;</span>       <span class="token comment">// 0x0D (1 byte)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>  <span class="token comment">// Total: 16 bytes</span>
</code></pre><p><strong>Critical Protocol Design Note:</strong><br>
The <strong>12-byte payload</strong> is the core of the entire RS485 communication protocol. This payload contains the essential Key-Value pair data that enables all communication between PC and slave devices for system configuration, user configuration, application data requests, and AI model data.</p>
<p><strong>Important Clarification: Protocol Layer Separation</strong></p>
<p>The driver processes the <strong>complete 16-byte frame</strong> (Header + ID + Payload + CRC + Trailer) including frame validation, error detection, function code routing, and address filtering.</p>
<p>User applications only handle the <strong>12-byte payload</strong> containing:</p>
<ul>
<li><strong>Key (4 bytes)</strong>: Command identifier (e.g., "S001", "U001", "A001")</li>
<li><strong>Value (8 bytes)</strong>: Data value (integers, floats, or structured data)</li>
</ul>
<p>This separation provides simplicity for users, reliability through driver-level error handling, and automatic routing based on function codes.</p>
<p><strong>Buffer Management Features:</strong></p>
<ul>
<li><strong>Thread-Safe Operations</strong>: All buffer operations are protected by synchronization mechanisms</li>
<li><strong>Fixed-Size Buffers</strong>: Eliminates dynamic memory allocation overhead</li>
<li><strong>Real-Time Performance</strong>: Optimized for low-latency communication in airborne environments</li>
<li><strong>Non-Blocking Design</strong>: Driver operations never block user threads</li>
<li><strong>Buffer Status Monitoring</strong>: Applications can query buffer usage and availability</li>
<li><strong>Buffer Overflow Prevention</strong>: Pre-transmission and pre-storage checks with FIFO guarantee and configurable overflow policies</li>
</ul>
<h3 id="23-non-blocking-communication-flow-design">2.3 Non-Blocking Communication Flow Design </h3>
<p><strong>Critical Design Principle: Asynchronous Request-Response Pattern</strong></p>
<p>The RS485 driver implements a two-phase communication pattern:</p>
<p><strong>Phase 1: Request Transmission (Non-blocking)</strong> - PC sends request and returns immediately<br>
<strong>Phase 2: Response Retrieval (Polling-based)</strong> - PC checks if data is ready and retrieves when available</p>
<p><strong>Benefits:</strong> Airborne environment compatibility, multi-slave efficiency, system responsiveness, and predictable timing.</p>
<h3 id="24-frame-processing-architecture">2.4 Frame Processing Architecture </h3>
<p><strong>Asynchronous Frame Processing Pipeline</strong></p>
<p>The driver implements a frame processing pipeline that operates independently of user threads, focusing on efficiently extracting and managing the <strong>12-byte payload data</strong>.</p>
<p><strong>Processing Flow:</strong></p>
<ol>
<li><strong>Receive Path</strong>: Hardware → Frame Parser → Payload Extractor → Buffer Check → User Application</li>
<li><strong>Transmit Path</strong>: User Application → Buffer Check → Frame Builder → Hardware</li>
</ol>
<p><strong>Buffer Flag Check Process:</strong></p>
<ul>
<li><strong>Before Transmission</strong>: Check uplink buffer flag for available space</li>
<li><strong>Before Storage</strong>: Check downlink buffer flag for incoming payload space</li>
<li><strong>FIFO Enforcement</strong>: Maintain strict FIFO ordering for payload data</li>
<li><strong>Overflow Prevention</strong>: Apply configured overflow policy when buffer is full</li>
</ul>
<p><strong>Frame Processing State Machine:</strong><br>
The driver uses a state machine to process incoming frames (WAITING_HEADER → READING_ID → READING_PAYLOAD → READING_CRC → READING_TRAILER → FRAME_COMPLETE) and routes completed frames to appropriate API categories based on function codes.</p>
<h3 id="22-rs485-electrical-interface">2.2 RS485 Electrical Interface </h3>
<p>The RS485 interface follows the TIA/EIA-485 standard with differential signaling, supporting up to 1200 meters distance, 10 Mbps data rates, and up to 32 devices on a single half-duplex bus. The USB-RS485-WE-1800-BT FTDI converter interfaces with the RS485 bus, with our RS485 Filter Driver adding protocol intelligence above the FTDI VCP Function Driver.</p>
<h2 id="3-api-specification">3. API Specification </h2>
<h3 id="30-communication-flow-and-protocol-overview">3.0 Communication Flow and Protocol Overview </h3>
<p><strong>Typical Communication Flow:</strong></p>
<ol>
<li><strong>S001 Broadcast</strong>: Master assigns slave address (broadcast to address 0)</li>
<li><strong>U-series Commands</strong>: Master configures user settings (targeted to specific slave)</li>
<li><strong>A-series Requests</strong>: Master requests data from slave</li>
<li><strong>Error Handling</strong>: CRC errors trigger automatic retry (up to 3 attempts)</li>
</ol>
<p><strong>Key Communication Principles:</strong></p>
<ul>
<li>Master initiates all communication</li>
<li>Slaves respond within 100ms response window</li>
<li>Broadcast commands (S-series) require single slave connection</li>
<li>CRC errors trigger automatic retry mechanism</li>
<li>Timeout errors may indicate hardware failure</li>
</ul>
<h3 id="31-operation-rules-for-avoiding-data-collisions">3.1 Operation Rules for Avoiding Data Collisions </h3>
<p><strong>Key Rules for Half-Duplex Master-Slave Communication:</strong></p>
<ol>
<li><strong>Master Responsibility</strong>: Master initiates all communication and controls bus flow</li>
<li><strong>Response Window</strong>: Slaves must respond within 100ms response window</li>
<li><strong>Transmitter Control</strong>: Slaves enable transmitter only when sending, disable immediately after</li>
<li><strong>Broadcasting Rules</strong>: Only master can broadcast (to address 0x00)</li>
<li><strong>Error Recovery</strong>: Master retries if no response received</li>
<li><strong>Bus Idle State</strong>: RS485 transceiver ensures proper start bit detection when idle</li>
</ol>
<h3 id="32-uart-frame-format">3.2 UART Frame Format </h3>
<p>The UART communication uses the following frame format:</p>
<table>
<thead>
<tr>
<th style="text-align:center">Start Bit</th>
<th style="text-align:center">Data Bits (0-7)</th>
<th style="text-align:center">Parity Bit</th>
<th style="text-align:center">Stop Bit</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">1 bit</td>
<td style="text-align:center">8 bits</td>
<td style="text-align:center">None</td>
<td style="text-align:center">1 bit</td>
</tr>
</tbody>
</table>
<p>Total: 10 bits per byte (1 start + 8 data + 0 parity + 1 stop)</p>
<p>This is commonly referred to as "8N1" format (8 data bits, No parity, 1 stop bit).</p>
<h3 id="33-zes-protocol-frame-format">3.3 ZES Protocol Frame Format </h3>
<p>The ZES protocol uses a 16-byte frame format as follows:</p>
<table>
<thead>
<tr>
<th style="text-align:center">Header</th>
<th style="text-align:center">ID Byte</th>
<th style="text-align:center">Data Payload (12 bytes)</th>
<th style="text-align:center">CRC8</th>
<th style="text-align:center">Trailer</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">0xAA</td>
<td style="text-align:center">1 byte</td>
<td style="text-align:center">Key (4 bytes) + Value (8 bytes)</td>
<td style="text-align:center">1 byte</td>
<td style="text-align:center">0x0D</td>
</tr>
</tbody>
</table>
<p><strong>Frame Structure Details:</strong></p>
<ul>
<li><strong>Header (0xAA)</strong>: Fixed start byte for frame synchronization</li>
<li><strong>ID Byte</strong>: Contains function code (3 bits) + device address (5 bits)</li>
<li><strong>Data Payload</strong>: 12 bytes containing Key-Value pair in JSON format
<ul>
<li>Key: 4 bytes (ASCII command identifier)</li>
<li>Value: 8 bytes (binary data value)</li>
</ul>
</li>
<li><strong>CRC8</strong>: Error detection using polynomial <span class="katex"><span class="katex-mathml"><math xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mn>0</mn><mi>x</mi><mn>97</mn><mo>=</mo><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><msup><mi>x</mi><mn>5</mn></msup><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1</annotation></semantics></math></span><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:0.6444em;"></span><span class="mord">0</span><span class="mord mathnormal">x</span><span class="mord">97</span><span class="mspace" style="margin-right:0.2778em;"></span><span class="mrel">=</span><span class="mspace" style="margin-right:0.2778em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">8</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">5</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">3</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">2</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.6667em;vertical-align:-0.0833em;"></span><span class="mord mathnormal">x</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.6444em;"></span><span class="mord">1</span></span></span></span></li>
<li><strong>Trailer (0x0D)</strong>: Fixed end byte for frame termination</li>
</ul>
<p><strong>CRC8 Implementation:</strong></p>
<ul>
<li>Covers 13 bytes from ID byte to data payload (excluding header byte)</li>
<li>Both master and slave must calculate and verify CRC</li>
<li>On CRC error, a re-send request frame is immediately transmitted</li>
<li>If header byte is corrupted, frame is automatically dropped (timeout-based recovery)</li>
</ul>
<p><strong>CRC8 Calculation Example:</strong><br>
For a frame with ID <code>0xE1</code> and payload <code>0x57 0x30 0x30 0x31 0x00 0x00 0x00 0x05 0x00 0x00 0x00 0x00</code>:</p>
<ol>
<li>Input data: 13 bytes (ID + 12-byte payload)</li>
<li>Polynomial: <span class="katex"><span class="katex-mathml"><math xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mn>0</mn><mi>x</mi><mn>97</mn><mo>=</mo><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><msup><mi>x</mi><mn>5</mn></msup><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1</annotation></semantics></math></span><span class="katex-html" aria-hidden="true"><span class="base"><span class="strut" style="height:0.6444em;"></span><span class="mord">0</span><span class="mord mathnormal">x</span><span class="mord">97</span><span class="mspace" style="margin-right:0.2778em;"></span><span class="mrel">=</span><span class="mspace" style="margin-right:0.2778em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">8</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">5</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">3</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.8974em;vertical-align:-0.0833em;"></span><span class="mord"><span class="mord mathnormal">x</span><span class="msupsub"><span class="vlist-t"><span class="vlist-r"><span class="vlist" style="height:0.8141em;"><span style="top:-3.063em;margin-right:0.05em;"><span class="pstrut" style="height:2.7em;"></span><span class="sizing reset-size6 size3 mtight"><span class="mord mtight"><span class="mord mtight">2</span></span></span></span></span></span></span></span></span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.6667em;vertical-align:-0.0833em;"></span><span class="mord mathnormal">x</span><span class="mspace" style="margin-right:0.2222em;"></span><span class="mbin">+</span><span class="mspace" style="margin-right:0.2222em;"></span></span><span class="base"><span class="strut" style="height:0.6444em;"></span><span class="mord">1</span></span></span></span></li>
<li>Initial value: 0x00</li>
<li>The CRC8 calculation processes each byte sequentially using the polynomial</li>
<li>Result: CRC byte to be placed in the frame</li>
</ol>
<p><strong>Error Handling and Retry Mechanism:</strong></p>
<ul>
<li>Upon detecting a CRC error, the receiver (master or slave) sends a re-send request frame (function code 0b000)</li>
<li>The sender retries up to 3 times maximum</li>
<li>If all retries fail, the communication is considered failed, and the master logs the error or takes corrective action</li>
<li>This provides a clear limit and aligns with robust error-handling practices</li>
</ul>
<p><strong>Re-send Request Strategy Clarification:</strong><br>
The ZES protocol defines function code 0b000 as "re-send request" for both master and slave use. The implementation strategy is:</p>
<ol>
<li>
<p><strong>Master-Initiated Retries</strong>: When the master detects a CRC error or timeout, it re-sends the original command (with original function code) rather than sending a separate 0b000 frame. This is simpler and sufficient for most cases.</p>
</li>
<li>
<p><strong>Slave-Initiated Re-send Requests</strong>: If a slave detects a CRC error in a received frame, it can send a re-send request frame (function code 0b000) to the master. However, the current API design focuses on master-initiated retries for simplicity.</p>
</li>
<li>
<p><strong>API Implementation</strong>: The driver handles retries automatically by re-transmitting the original command. The 0b000 function code is primarily used internally for protocol compliance but is not directly exposed in the high-level API.</p>
</li>
</ol>
<p>This approach provides robust error recovery while maintaining API simplicity.</p>
<h4 id="331-id-byte-structure">3.3.1 ID Byte Structure </h4>
<p>The ID byte is structured as follows:</p>
<table>
<thead>
<tr>
<th style="text-align:center">Function Code (3 bits)</th>
<th style="text-align:center">Device Address (5 bits)</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">High 3 bits</td>
<td style="text-align:center">Low 5 bits</td>
</tr>
</tbody>
</table>
<p>The 5-bit address field allows for a maximum of 31 nodes (30 slaves plus one master). Address 0x00 is reserved for broadcast messages. All AI-SLDAP boards are initialized with a default address of 0x00 during manufacturing.</p>
<p>Function codes:</p>
<ul>
<li>0b111: Assign data (Master use)</li>
<li>0b110: Request data (Master use)</li>
<li>0b010: Response to Assign (Slave use)</li>
<li>0b001: Response to Request (Slave use)</li>
<li>0b000: Re-send request (Both use)</li>
</ul>
<h4 id="332-data-payload-structure-by-category">3.3.2 Data Payload Structure by Category </h4>
<p>The data payload consists of a JSON-style key-value pair as defined in the ZES protocol:</p>
<p><strong>Key-Value Pair Format:</strong></p>
<ul>
<li><strong>Key Field</strong>: 4 bytes (higher 4 bytes of payload) - holds the name of the variable in ASCII code</li>
<li><strong>Value Field</strong>: 8 bytes (lower 8 bytes of payload) - holds the value of the variable in binary format</li>
</ul>
<p><strong>Message Categories (as defined in ZES protocol):</strong></p>
<table>
<thead>
<tr>
<th>Key Code</th>
<th>Category</th>
<th>Used For</th>
<th>Examples</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>"Sxxx"</strong></td>
<td>System configurable data</td>
<td>Assign AI-SLDAP system parameters</td>
<td>S001 (slave address), S002 (baud rate)</td>
</tr>
<tr>
<td><strong>"Uxxx"</strong></td>
<td>User configurable data</td>
<td>Configure/control how AISL works</td>
<td>U001-U006 (various user settings)</td>
</tr>
<tr>
<td><strong>"Axxx"</strong></td>
<td>Application related data</td>
<td>Information/status reports</td>
<td>A001 (SEL event log), A002 (device status), A003 (firmware version), A004 (system statistics), A005 (current configuration)</td>
</tr>
<tr>
<td><strong>"Wxxx"</strong></td>
<td>Weights of AI model</td>
<td>Transmit weight parameters from PC to AISL</td>
<td>W001 (write), W002 (read)</td>
</tr>
</tbody>
</table>
<p><strong>Data Payload Structure by API Category:</strong></p>
<p><strong>Category 1: System Configuration (Broadcasting)</strong></p>
<ul>
<li>Key (4 bytes): Command identifier (e.g., "S001", "S002" in ASCII)</li>
<li>Value (8 bytes): Configuration value to be assigned to the FPGA register</li>
</ul>
<p><strong>Category 2: Master to Slave Request</strong></p>
<ul>
<li>Key (4 bytes): Data identifier for the requested information (e.g., "A001", "A002" in ASCII)</li>
<li>Value (8 bytes): Reserved for future use (typically set to 0)</li>
</ul>
<p><strong>Category 3: AI Model Weight and Bias Data</strong></p>
<ul>
<li>Key (4 bytes): Memory address in FRAM (for "W001", "W002" operations)</li>
<li>Value (8 bytes): Data to be written (for write operations) or length of data to read (for read operations)</li>
</ul>
<h4 id="333-user-interaction-with-protocol">3.3.3 User Interaction with Protocol </h4>
<p>From the user's perspective, the protocol details are abstracted away. Users interact with the three API categories without needing to know about the ID byte structure or how the data is formatted in the payload. The driver automatically handles the construction of appropriate messages based on the API call, including setting the correct function code and device address in the ID byte.</p>
<h3 id="34-windows-driver-interface-structure">3.4 Windows Driver Interface Structure </h3>
<p>The RS485 driver is implemented as a Windows User-Mode Driver Framework (UMDF) driver, providing a native Windows driver interface instead of a traditional C++ class library. Applications interact with the driver through Windows I/O Control (IOCTL) calls using <strong>DeviceIoControl()</strong>.</p>
<p><strong>Data Exchange Mechanism:</strong><br>
Data exchange between user-mode applications and the RS485 driver is achieved using <strong>DeviceIoControl()</strong> calls with specific IOCTL codes. This approach provides:</p>
<ol>
<li><strong>Standard Windows Interface</strong>: Uses the industry-standard Windows driver communication method</li>
<li><strong>Asynchronous I/O Support</strong>: Enables non-blocking operations with overlapped I/O</li>
<li><strong>Buffer Management</strong>: Efficient data transfer through system-managed buffers</li>
<li><strong>Error Handling</strong>: Comprehensive error reporting through Windows error codes</li>
</ol>
<p><strong>DeviceIoControl() Implementation Strategy:</strong></p>
<ul>
<li><strong>API Layer</strong>: The high-level API functions (configureSystemSettings, requestData, etc.) internally use DeviceIoControl() calls</li>
<li><strong>Abstraction</strong>: Users interact with the simplified API interface without directly calling DeviceIoControl()</li>
<li><strong>Internal Implementation</strong>: Each API function translates to appropriate IOCTL codes and buffer management</li>
<li><strong>FIFO Guarantee</strong>: DeviceIoControl() calls are queued and processed in strict FIFO order to maintain data integrity</li>
</ul>
<p><strong>Complete Driver Interface Architecture with Function Code Mapping:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Windows Driver Interface - Application Side</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">AI_SLDAP_RS485_DriverInterface</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Constructor and basic operations</span>
    <span class="token function">AI_SLDAP_RS485_DriverInterface</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token operator">~</span><span class="token function">AI_SLDAP_RS485_DriverInterface</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// ===== ERROR HANDLE API (Function Code: 0b000) =====</span>
    <span class="token comment">// FTDI-style management functions</span>
    ConnectionResult <span class="token function">openPort</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>wstring<span class="token operator">&amp;</span> devicePath<span class="token punctuation">)</span><span class="token punctuation">;</span>
    ConnectionResult <span class="token function">closePort</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">isPortOpen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>
    PortResult <span class="token function">getPortInfo</span><span class="token punctuation">(</span>PortInfo<span class="token operator">&amp;</span> info<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Device enumeration (similar to FTDI FT_ListDevices)</span>
    <span class="token keyword keyword-static">static</span> EnumerationResult <span class="token function">enumerateDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DeviceInfo<span class="token operator">&gt;</span><span class="token operator">&amp;</span> deviceList<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> DetectionResult <span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> detectedAddresses<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Buffer management - CRITICAL for data integrity</span>
    BufferResult <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>BufferStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">checkUplinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">checkDownlinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">clearBuffer</span><span class="token punctuation">(</span>BufferType bufferType <span class="token operator">=</span> BufferType<span class="token double-colon punctuation">::</span>BOTH<span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">setBufferOverflowPolicy</span><span class="token punctuation">(</span>BufferOverflowPolicy policy<span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">getBufferCapacity</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> uplinkFrames<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> downlinkFrames<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Hardware status (similar to FTDI FT_GetStatus)</span>
    HardwareResult <span class="token function">getHardwareStatus</span><span class="token punctuation">(</span>HardwareStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Performance monitoring - provides communication statistics and throughput metrics</span>
    <span class="token comment">// Returns: frame transmission rates, error rates, buffer utilization, retry counts</span>
    PerformanceResult <span class="token function">getPerformanceMetrics</span><span class="token punctuation">(</span>PerformanceMetrics<span class="token operator">&amp;</span> metrics<span class="token punctuation">)</span><span class="token punctuation">;</span>

    ConfigResult <span class="token function">getBaudRate</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> currentBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Line status monitoring - provides real-time RS485 bus and hardware status</span>
    <span class="token comment">// Returns: signal levels, carrier detect, data set ready, ring indicator status</span>
    LineResult <span class="token function">getLineStatus</span><span class="token punctuation">(</span>LineStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Error handling and retry management</span>
    <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span> <span class="token function">getErrorString</span><span class="token punctuation">(</span>RS485Error error<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-using">using</span> ErrorCallbackFn <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>function<span class="token operator">&lt;</span><span class="token keyword keyword-void">void</span><span class="token punctuation">(</span>RS485Error error<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span> message<span class="token punctuation">)</span><span class="token operator">&gt;</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">registerErrorCallback</span><span class="token punctuation">(</span>ErrorCallbackFn callback<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">unregisterErrorCallback</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// ===== MASTER BROADCASTING API (Function Code: 0b111 for S-series) =====</span>
    <span class="token comment">// Automatic buffer flag checking before transmission</span>
    <span class="token comment">// commandKey: 4-byte command identifier (e.g., "S001", "S002")</span>
    <span class="token comment">// value: 8-byte payload data</span>
    ConfigurationResult <span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>
    VerificationResult <span class="token function">verifySystemConfig</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> expectedValue<span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isMatching<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// ===== MASTER ASSIGN DATA API (Function Code: 0b111 for U/W-series) =====</span>
    <span class="token comment">// Automatic buffer flag checking before transmission</span>
    <span class="token comment">// commandKey: 4-byte command identifier (e.g., "U001", "U002", etc.)</span>
    <span class="token comment">// value: 8-byte payload data</span>
    ConfigurationResult <span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>
    ModelDataResult <span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> address<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> data<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span> isWrite<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> length <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// ===== MASTER REQUEST API (Function Code: 0b110 for A-series) =====</span>
    <span class="token comment">// Automatic buffer flag checking before transmission</span>
    <span class="token comment">// Uses slave address previously set by S001 command</span>
    <span class="token comment">// dataKey: 4-byte command identifier (e.g., "A001", "A002", etc.)</span>
    RequestResult <span class="token function">requestData</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> dataKey<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> RequestOptions<span class="token operator">*</span> options <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// ===== SLAVE RESPONSE API (Function Codes: 0b010 and 0b001) =====</span>
    <span class="token comment">// Non-blocking design: PC requests data, then separately checks for response availability</span>
    <span class="token comment">// Step 1: Check if slave has prepared response data (non-blocking)</span>
    ResponseResult <span class="token function">checkSlaveDataReady</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isDataReady<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Step 2: Retrieve prepared response data (only when data is ready)</span>
    <span class="token comment">// Uses fixed-size array for better performance and memory predictability</span>
    ResponseResult <span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> timeout <span class="token operator">=</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Response callback registration for asynchronous handling</span>
    <span class="token comment">// Uses fixed-size array for better performance and cross-platform compatibility</span>
    <span class="token keyword keyword-using">using</span> ResponseCallbackFn <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>function<span class="token operator">&lt;</span><span class="token keyword keyword-void">void</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> data<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token operator">&gt;</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">registerResponseCallback</span><span class="token punctuation">(</span>ResponseCallbackFn callback<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">unregisterResponseCallback</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Buffer threshold monitoring</span>
    BufferResult <span class="token function">setBufferThreshold</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> thresholdPercent<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-using">using</span> BufferThresholdCallbackFn <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>function<span class="token operator">&lt;</span><span class="token keyword keyword-void">void</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> currentUsage<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> totalSize<span class="token punctuation">)</span><span class="token operator">&gt;</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">registerBufferThresholdCallback</span><span class="token punctuation">(</span>BufferThresholdCallbackFn callback<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token comment">// Windows driver communication</span>
    HANDLE m_driverHandle<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>wstring m_devicePath<span class="token punctuation">;</span>

    <span class="token comment">// IOCTL helper methods with automatic buffer checking</span>
    IOCTLResult <span class="token function">sendIOCTL</span><span class="token punctuation">(</span>DWORD ioctlCode<span class="token punctuation">,</span> <span class="token keyword keyword-void">void</span><span class="token operator">*</span> inputBuffer<span class="token punctuation">,</span> DWORD inputSize<span class="token punctuation">,</span>
                        <span class="token keyword keyword-void">void</span><span class="token operator">*</span> outputBuffer<span class="token punctuation">,</span> DWORD outputSize<span class="token punctuation">,</span> DWORD<span class="token operator">*</span> bytesReturned <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Buffer flag checking - called before every transmission</span>
    BufferResult <span class="token function">checkBufferBeforeTransmission</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    BufferResult <span class="token function">checkBufferBeforeStorage</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Thread safety</span>
    std<span class="token double-colon punctuation">::</span>mutex m_apiMutex<span class="token punctuation">;</span>

    <span class="token comment">// Current slave address for U-series commands</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> m_currentSlaveAddress<span class="token punctuation">;</span>

    <span class="token comment">// Buffer management state</span>
    BufferOverflowPolicy m_bufferOverflowPolicy<span class="token punctuation">;</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> m_bufferThresholdPercent<span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// ===== IMPROVED ERROR TYPE DEFINITIONS =====</span>
<span class="token comment">// Specific result types for different API categories to improve code clarity and maintainability</span>

<span class="token comment">// Connection and port management results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ConnectionResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    PORT_NOT_FOUND<span class="token punctuation">,</span>
    PORT_ALREADY_OPEN<span class="token punctuation">,</span>
    PORT_ACCESS_DENIED<span class="token punctuation">,</span>
    INVALID_PORT_NAME<span class="token punctuation">,</span>
    CONNECTION_FAILED<span class="token punctuation">,</span>
    TIMEOUT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Device enumeration and detection results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">EnumerationResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    NO_DEVICES_FOUND<span class="token punctuation">,</span>
    SYSTEM_ERROR<span class="token punctuation">,</span>
    INSUFFICIENT_BUFFER<span class="token punctuation">,</span>
    ACCESS_DENIED
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">DetectionResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    MULTIPLE_DEVICES_DETECTED<span class="token punctuation">,</span>
    NO_RESPONSE<span class="token punctuation">,</span>
    COMMUNICATION_ERROR<span class="token punctuation">,</span>
    TIMEOUT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Buffer management results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">BufferResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    INSUFFICIENT_BUFFER<span class="token punctuation">,</span>
    BUFFER_OVERFLOW<span class="token punctuation">,</span>
    BUFFER_UNDERFLOW<span class="token punctuation">,</span>
    INVALID_BUFFER_TYPE<span class="token punctuation">,</span>
    BUFFER_NOT_INITIALIZED
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Hardware and performance monitoring results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">HardwareResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    HARDWARE_NOT_READY<span class="token punctuation">,</span>
    HARDWARE_ERROR<span class="token punctuation">,</span>
    SENSOR_FAILURE<span class="token punctuation">,</span>
    COMMUNICATION_LOST
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">PerformanceResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    METRICS_NOT_AVAILABLE<span class="token punctuation">,</span>
    INSUFFICIENT_DATA<span class="token punctuation">,</span>
    CALCULATION_ERROR
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">LineResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    LINE_NOT_READY<span class="token punctuation">,</span>
    SIGNAL_ERROR<span class="token punctuation">,</span>
    BUS_FAULT<span class="token punctuation">,</span>
    ELECTRICAL_FAULT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Configuration and data operation results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ConfigurationResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    INVALID_COMMAND<span class="token punctuation">,</span>
    INVALID_VALUE<span class="token punctuation">,</span>
    DEVICE_NOT_RESPONDING<span class="token punctuation">,</span>
    CONFIGURATION_FAILED<span class="token punctuation">,</span>
    VERIFICATION_FAILED<span class="token punctuation">,</span>
    BUFFER_FULL<span class="token punctuation">,</span>
    TIMEOUT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">VerificationResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    MISMATCH_DETECTED<span class="token punctuation">,</span>
    VERIFICATION_FAILED<span class="token punctuation">,</span>
    DEVICE_NOT_RESPONDING<span class="token punctuation">,</span>
    TIMEOUT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ModelDataResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    INVALID_ADDRESS<span class="token punctuation">,</span>
    INVALID_DATA_LENGTH<span class="token punctuation">,</span>
    WRITE_FAILED<span class="token punctuation">,</span>
    READ_FAILED<span class="token punctuation">,</span>
    MEMORY_ERROR<span class="token punctuation">,</span>
    TIMEOUT
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Request and response results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">RequestResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    INVALID_REQUEST<span class="token punctuation">,</span>
    DEVICE_NOT_RESPONDING<span class="token punctuation">,</span>
    REQUEST_TIMEOUT<span class="token punctuation">,</span>
    BUFFER_FULL
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ResponseResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    NO_DATA_AVAILABLE<span class="token punctuation">,</span>
    INVALID_RESPONSE<span class="token punctuation">,</span>
    RESPONSE_TIMEOUT<span class="token punctuation">,</span>
    BUFFER_EMPTY<span class="token punctuation">,</span>
    BUFFER_FULL<span class="token punctuation">,</span>
    CRC_ERROR
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Port information results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">PortResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    PORT_NOT_OPEN<span class="token punctuation">,</span>
    INVALID_PORT<span class="token punctuation">,</span>
    INFO_NOT_AVAILABLE
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Configuration query results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ConfigResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    CONFIG_NOT_AVAILABLE<span class="token punctuation">,</span>
    INVALID_CONFIG<span class="token punctuation">,</span>
    READ_ERROR
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// IOCTL operation results</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">IOCTLResult</span> <span class="token punctuation">{</span>
    SUCCESS<span class="token punctuation">,</span>
    INVALID_IOCTL_CODE<span class="token punctuation">,</span>
    BUFFER_TOO_SMALL<span class="token punctuation">,</span>
    DEVICE_ERROR<span class="token punctuation">,</span>
    OPERATION_FAILED
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Driver-specific structures for payload buffer management</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">BufferStatus</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkUsed<span class="token punctuation">;</span>        <span class="token comment">// Used payload slots in uplink buffer (0-5)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkTotal<span class="token punctuation">;</span>       <span class="token comment">// Total uplink buffer capacity (5 payload slots)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkUsed<span class="token punctuation">;</span>      <span class="token comment">// Used payload slots in downlink buffer (0-10)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkTotal<span class="token punctuation">;</span>     <span class="token comment">// Total downlink buffer capacity (10 payload slots)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> payloadSize<span class="token punctuation">;</span>       <span class="token comment">// Size per payload slot (12 bytes)</span>
    <span class="token keyword keyword-bool">bool</span> isUplinkFull<span class="token punctuation">;</span>          <span class="token comment">// Uplink buffer full flag</span>
    <span class="token keyword keyword-bool">bool</span> isDownlinkFull<span class="token punctuation">;</span>        <span class="token comment">// Downlink buffer full flag</span>
    <span class="token keyword keyword-bool">bool</span> isOverflowDetected<span class="token punctuation">;</span>    <span class="token comment">// Buffer overflow status</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> totalBufferBytes<span class="token punctuation">;</span>  <span class="token comment">// Total buffer capacity in bytes (60 + 120 = 180 bytes)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">BufferType</span> <span class="token punctuation">{</span>
    UPLINK<span class="token punctuation">,</span>     <span class="token comment">// PC to device buffer</span>
    DOWNLINK<span class="token punctuation">,</span>   <span class="token comment">// Device to PC buffer</span>
    BOTH        <span class="token comment">// Both buffers</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">BufferOverflowPolicy</span> <span class="token punctuation">{</span>
    TRIGGER_ERROR<span class="token punctuation">,</span>   <span class="token comment">// Return error when buffer is full (RECOMMENDED - prevents data loss)</span>
    DISCARD_OLDEST<span class="token punctuation">,</span>  <span class="token comment">// Discard oldest frame when buffer is full</span>
    DISCARD_NEWEST   <span class="token comment">// Discard new frame when buffer is full</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token operator">*</span><span class="token operator">*</span>Frame<span class="token operator">-</span>by<span class="token operator">-</span>Frame Transmission Control<span class="token operator">:</span><span class="token operator">*</span><span class="token operator">*</span>
```cpp
<span class="token comment">// Example: Sending multiple frames with buffer overflow protection</span>
ConfigurationResult <span class="token function">sendMultipleFrames</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>FrameData<span class="token operator">&gt;</span><span class="token operator">&amp;</span> frames<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> frame <span class="token operator">:</span> frames<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Check buffer availability before each frame</span>
        BufferStatus status<span class="token punctuation">;</span>
        BufferResult bufferCheck <span class="token operator">=</span> <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferCheck <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>BUFFER_ERROR<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// If buffer is full, stop transmission and return error</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>status<span class="token punctuation">.</span>isUplinkFull<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>BUFFER_FULL<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Send frame only if buffer has space</span>
        ConfigurationResult result <span class="token operator">=</span> <span class="token function">sendSingleFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><pre data-role="codeBlock" data-info="" class="language-text"><code>**Windows Driver IOCTL Codes:**
```cpp
// IOCTL codes for driver communication
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_MODEL_DATA_OP       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_BUFFER_POLICY   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HW_STATUS       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x808, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_GET_PERFORMANCE     CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x809, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CHECK_BUFFER_FLAGS  CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x80A, METHOD_BUFFERED, FILE_READ_ACCESS)
</code></pre><h3 id="35-deviceiocontrol-implementation-details-and-memory-space-access">3.5 DeviceIoControl() Implementation Details and Memory Space Access </h3>
<p><strong>API Design Philosophy:</strong><br>
The DeviceIoControl() mechanism is implemented <strong>internally within the API functions</strong> rather than being exposed directly to users. This design provides:</p>
<ol>
<li><strong>User-Friendly Interface</strong>: Applications use high-level API functions (configureSystemSettings, requestData, etc.)</li>
<li><strong>Internal Implementation</strong>: Each API function internally calls DeviceIoControl() with appropriate IOCTL codes</li>
<li><strong>Abstraction Layer</strong>: Users don't need to understand IOCTL codes or buffer management details</li>
<li><strong>Industry Standard</strong>: Follows standard serial port communication interface patterns</li>
</ol>
<p><strong>Memory Space Access via DeviceIoControl:</strong><br>
DeviceIoControl enables the RS485 driver to access different memory spaces efficiently:</p>
<ol>
<li><strong>User Application Memory</strong>: Driver can read/write user-provided buffers through IOCTL input/output parameters</li>
<li><strong>Driver Internal Memory</strong>: Driver maintains its own buffer management system for payload data</li>
<li><strong>Hardware Device Memory</strong>: Driver communicates with FTDI hardware through lower-level APIs</li>
<li><strong>Cross-Process Communication</strong>: DeviceIoControl provides secure kernel-mediated data exchange</li>
</ol>
<p><strong>Why DeviceIoControl is Ideal for RS485 Communication:</strong></p>
<ul>
<li><strong>Kernel-Mode Access</strong>: Enables direct hardware communication with proper privilege separation</li>
<li><strong>Buffer Management</strong>: Supports both input and output buffers for bidirectional data transfer</li>
<li><strong>Asynchronous Operations</strong>: Supports overlapped I/O for non-blocking communication</li>
<li><strong>Error Handling</strong>: Provides comprehensive error reporting through Windows error codes</li>
<li><strong>Security</strong>: Kernel validates all memory access operations to prevent buffer overflows</li>
</ul>
<p><strong>Improved Communication Pattern Example:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Example: Non-blocking request-response pattern with buffer management</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">RS485CommunicationExample</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Step 1: Send request (non-blocking)</span>
    RequestResult <span class="token function">sendDataRequest</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> dataKey<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Check uplink buffer before sending</span>
        BufferStatus status<span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>driver<span class="token punctuation">.</span><span class="token function">getBufferStatus</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span> <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> RequestResult<span class="token double-colon punctuation">::</span>BUFFER_ERROR<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>status<span class="token punctuation">.</span>isUplinkFull<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> RequestResult<span class="token double-colon punctuation">::</span>BUFFER_FULL<span class="token punctuation">;</span>  <span class="token comment">// Prevent overflow</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Send request and return immediately</span>
        <span class="token keyword keyword-return">return</span> driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span>dataKey<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 2: Check if response is ready (non-blocking)</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">checkResponseAvailable</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-bool">bool</span> isDataReady <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>
        ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">checkSlaveDataReady</span><span class="token punctuation">(</span>isDataReady<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS <span class="token operator">&amp;&amp;</span> isDataReady<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 3: Retrieve response data (only when ready)</span>
    ResponseResult <span class="token function">getResponseData</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Complete communication example</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">communicateWithSlave</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> dataKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Phase 1: Send request</span>
        RequestResult requestResult <span class="token operator">=</span> <span class="token function">sendDataRequest</span><span class="token punctuation">(</span>dataKey<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>requestResult <span class="token operator">!=</span> RequestResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> <span class="token boolean">false</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Phase 2: Poll for response (with timeout)</span>
        <span class="token keyword keyword-auto">auto</span> startTime <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>steady_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span> timeout <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token function">milliseconds</span><span class="token punctuation">(</span><span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-while">while</span> <span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>steady_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">-</span> startTime <span class="token operator">&lt;</span> timeout<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">checkResponseAvailable</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token comment">// Phase 3: Retrieve data</span>
                ResponseResult responseResult <span class="token operator">=</span> <span class="token function">getResponseData</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span>responseResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
            std<span class="token double-colon punctuation">::</span>this_thread<span class="token double-colon punctuation">::</span><span class="token function">sleep_for</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token function">milliseconds</span><span class="token punctuation">(</span><span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Poll every 10ms</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-return">return</span> <span class="token boolean">false</span><span class="token punctuation">;</span>  <span class="token comment">// Timeout</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>Original Buffer Management Implementation Example:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Example: Internal implementation with comprehensive buffer checking</span>
ConfigurationResult <span class="token class-name">AI_SLDAP_RS485_DriverInterface</span><span class="token double-colon punctuation">::</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>lock_guard<span class="token operator">&lt;</span>std<span class="token double-colon punctuation">::</span>mutex<span class="token operator">&gt;</span> <span class="token function">lock</span><span class="token punctuation">(</span>m_apiMutex<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Step 1: Mandatory buffer flag checking before transmission</span>
    BufferResult bufferCheck <span class="token operator">=</span> <span class="token function">checkBufferBeforeTransmission</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferCheck <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>BUFFER_FULL<span class="token punctuation">;</span>  <span class="token comment">// Convert buffer error to configuration error</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 2: Validate function code correspondence (S-series uses 0b111)</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token function">isValidSystemCommand</span><span class="token punctuation">(</span>commandKey<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>INVALID_COMMAND<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 3: Prepare IOCTL input buffer with 12-byte payload</span>
    <span class="token keyword keyword-struct">struct</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> key<span class="token punctuation">;</span>           <span class="token comment">// 4 bytes - command key</span>
        <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">;</span>         <span class="token comment">// 8 bytes - command value</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> functionCode<span class="token punctuation">;</span>   <span class="token comment">// Function code 0b111 for assign data</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">;</span>   <span class="token comment">// 0x00 for broadcast</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> reserved<span class="token punctuation">[</span><span class="token number">2</span><span class="token punctuation">]</span><span class="token punctuation">;</span>    <span class="token comment">// Padding for alignment</span>
    <span class="token punctuation">}</span> inputBuffer <span class="token operator">=</span> <span class="token punctuation">{</span>
        commandKey<span class="token punctuation">,</span>
        value<span class="token punctuation">,</span>
        <span class="token number">0b111</span><span class="token punctuation">,</span>  <span class="token comment">// Assign data function code</span>
        <span class="token number">0x00</span><span class="token punctuation">,</span>   <span class="token comment">// Broadcast address</span>
        <span class="token punctuation">{</span><span class="token number">0</span><span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">}</span>  <span class="token comment">// Reserved</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>

    <span class="token comment">// Step 4: Call DeviceIoControl() with buffer management</span>
    DWORD bytesReturned<span class="token punctuation">;</span>
    BOOL result <span class="token operator">=</span> <span class="token function">DeviceIoControl</span><span class="token punctuation">(</span>
        m_driverHandle<span class="token punctuation">,</span>                    <span class="token comment">// Device handle</span>
        IOCTL_RS485_CONFIGURE_SYSTEM<span class="token punctuation">,</span>      <span class="token comment">// IOCTL code</span>
        <span class="token operator">&amp;</span>inputBuffer<span class="token punctuation">,</span>                      <span class="token comment">// Input buffer</span>
        <span class="token keyword keyword-sizeof">sizeof</span><span class="token punctuation">(</span>inputBuffer<span class="token punctuation">)</span><span class="token punctuation">,</span>               <span class="token comment">// Input buffer size</span>
        <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">,</span>                           <span class="token comment">// Output buffer (not needed)</span>
        <span class="token number">0</span><span class="token punctuation">,</span>                                 <span class="token comment">// Output buffer size</span>
        <span class="token operator">&amp;</span>bytesReturned<span class="token punctuation">,</span>                    <span class="token comment">// Bytes returned</span>
        <span class="token keyword keyword-nullptr">nullptr</span>                            <span class="token comment">// Overlapped (for async operation)</span>
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>result<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        DWORD lastError <span class="token operator">=</span> <span class="token function">GetLastError</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token function">mapWindowsErrorToRS485Error</span><span class="token punctuation">(</span>lastError<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 5: For broadcast commands, no acknowledgment expected</span>
    <span class="token comment">// For targeted commands, use separate checkSlaveDataReady() and receiveSlaveResponse()</span>
    <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Improved buffer checking implementation with overflow prevention</span>
BufferResult <span class="token class-name">AI_SLDAP_RS485_DriverInterface</span><span class="token double-colon punctuation">::</span><span class="token function">checkBufferBeforeTransmission</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    BufferStatus status<span class="token punctuation">;</span>
    BufferResult result <span class="token operator">=</span> <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Critical: Check uplink buffer flag before transmission</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>status<span class="token punctuation">.</span>isUplinkFull<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// RECOMMENDED: Always return error to prevent data loss</span>
        <span class="token keyword keyword-return">return</span> BufferResult<span class="token double-colon punctuation">::</span>INSUFFICIENT_BUFFER<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Check if buffer usage exceeds threshold (early warning)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> usagePercent <span class="token operator">=</span> <span class="token punctuation">(</span>status<span class="token punctuation">.</span>uplinkUsed <span class="token operator">*</span> <span class="token number">100</span><span class="token punctuation">)</span> <span class="token operator">/</span> status<span class="token punctuation">.</span>uplinkTotal<span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>usagePercent <span class="token operator">&gt;=</span> m_bufferThresholdPercent<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Trigger threshold callback if registered</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_bufferThresholdCallback<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token function">m_bufferThresholdCallback</span><span class="token punctuation">(</span>status<span class="token punctuation">.</span>uplinkUsed<span class="token punctuation">,</span> status<span class="token punctuation">.</span>uplinkTotal<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-return">return</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Frame-by-frame transmission with buffer checking</span>
ConfigurationResult <span class="token function">sendFrameWithBufferCheck</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> FrameData<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Mandatory buffer check before each frame</span>
    BufferResult bufferCheck <span class="token operator">=</span> <span class="token function">checkBufferBeforeTransmission</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferCheck <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>BUFFER_FULL<span class="token punctuation">;</span>  <span class="token comment">// Stop transmission</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Proceed with frame transmission only if buffer has space</span>
    <span class="token keyword keyword-return">return</span> <span class="token function">transmitSingleFrame</span><span class="token punctuation">(</span>frame<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Function code validation</span>
<span class="token keyword keyword-bool">bool</span> <span class="token class-name">AI_SLDAP_RS485_DriverInterface</span><span class="token double-colon punctuation">::</span><span class="token function">isValidSystemCommand</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// S-series commands: S001, S002</span>
    <span class="token keyword keyword-return">return</span> <span class="token punctuation">(</span>commandKey <span class="token operator">==</span> <span class="token string">"S001"</span> <span class="token operator">||</span> commandKey <span class="token operator">==</span> <span class="token string">"S002"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>Buffer Flag Management:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Buffer flag checking mechanism</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">BufferFlags</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-bool">bool</span> uplinkFull<span class="token punctuation">;</span>        <span class="token comment">// Uplink buffer full flag</span>
    <span class="token keyword keyword-bool">bool</span> downlinkFull<span class="token punctuation">;</span>      <span class="token comment">// Downlink buffer full flag</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkUsed<span class="token punctuation">;</span>    <span class="token comment">// Current uplink usage (0-5)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkUsed<span class="token punctuation">;</span>  <span class="token comment">// Current downlink usage (0-10)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Check buffer flags before operations</span>
RS485Error <span class="token function">checkBufferFlags</span><span class="token punctuation">(</span>BufferFlags<span class="token operator">&amp;</span> flags<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h2 id="4-detailed-api-reference">4. Detailed API Reference </h2>
<h3 id="40-error-codes">4.0 Error Codes </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">RS485Error</span> <span class="token punctuation">{</span>
    SUCCESS <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">,</span>

    <span class="token comment">// FTDI Driver errors (100-199)</span>
    CONNECTION_ERROR <span class="token operator">=</span> <span class="token number">100</span><span class="token punctuation">,</span> DEVICE_NOT_FOUND <span class="token operator">=</span> <span class="token number">101</span><span class="token punctuation">,</span> DEVICE_BUSY <span class="token operator">=</span> <span class="token number">102</span><span class="token punctuation">,</span>
    PORT_NOT_AVAILABLE <span class="token operator">=</span> <span class="token number">103</span><span class="token punctuation">,</span> DRIVER_NOT_LOADED <span class="token operator">=</span> <span class="token number">104</span><span class="token punctuation">,</span> INSUFFICIENT_RESOURCES <span class="token operator">=</span> <span class="token number">105</span><span class="token punctuation">,</span>
    INVALID_HANDLE <span class="token operator">=</span> <span class="token number">106</span><span class="token punctuation">,</span> INVALID_BAUD_RATE <span class="token operator">=</span> <span class="token number">107</span><span class="token punctuation">,</span> INVALID_PARAMETER <span class="token operator">=</span> <span class="token number">108</span><span class="token punctuation">,</span>

    <span class="token comment">// Buffer Management errors (150-199)</span>
    INSUFFICIENT_BUFFER <span class="token operator">=</span> <span class="token number">150</span><span class="token punctuation">,</span> BUFFER_OVERFLOW <span class="token operator">=</span> <span class="token number">151</span><span class="token punctuation">,</span> BUFFER_UNDERFLOW <span class="token operator">=</span> <span class="token number">152</span><span class="token punctuation">,</span>
    BUFFER_ALLOCATION_FAILED <span class="token operator">=</span> <span class="token number">153</span><span class="token punctuation">,</span> INVALID_BUFFER_SIZE <span class="token operator">=</span> <span class="token number">154</span><span class="token punctuation">,</span>

    <span class="token comment">// ZES Protocol errors (200-299)</span>
    PROTOCOL_ERROR <span class="token operator">=</span> <span class="token number">200</span><span class="token punctuation">,</span> CRC_ERROR <span class="token operator">=</span> <span class="token number">201</span><span class="token punctuation">,</span> TIMEOUT_ERROR <span class="token operator">=</span> <span class="token number">202</span><span class="token punctuation">,</span>
    FRAME_SYNC_ERROR <span class="token operator">=</span> <span class="token number">203</span><span class="token punctuation">,</span> RETRY_LIMIT_EXCEEDED <span class="token operator">=</span> <span class="token number">204</span><span class="token punctuation">,</span> UNSUPPORTED_OPERATION <span class="token operator">=</span> <span class="token number">205</span><span class="token punctuation">,</span>
    INVALID_COMMAND_KEY <span class="token operator">=</span> <span class="token number">206</span><span class="token punctuation">,</span> INVALID_SLAVE_ADDRESS <span class="token operator">=</span> <span class="token number">207</span><span class="token punctuation">,</span> BROADCAST_CONFLICT <span class="token operator">=</span> <span class="token number">208</span><span class="token punctuation">,</span>

    <span class="token comment">// Memory operation errors (300-399)</span>
    MEMORY_ACCESS_ERROR <span class="token operator">=</span> <span class="token number">300</span><span class="token punctuation">,</span> MEMORY_WRITE_FAILED <span class="token operator">=</span> <span class="token number">301</span><span class="token punctuation">,</span> MEMORY_READ_FAILED <span class="token operator">=</span> <span class="token number">302</span><span class="token punctuation">,</span>
    INVALID_MEMORY_ADDRESS <span class="token operator">=</span> <span class="token number">303</span><span class="token punctuation">,</span> MEMORY_RANGE_ERROR <span class="token operator">=</span> <span class="token number">304</span><span class="token punctuation">,</span> MEMORY_PROTECTION_ERROR <span class="token operator">=</span> <span class="token number">305</span><span class="token punctuation">,</span>

    <span class="token comment">// Data handling errors (400-499)</span>
    DATA_FORMAT_ERROR <span class="token operator">=</span> <span class="token number">400</span><span class="token punctuation">,</span> PAYLOAD_SIZE_ERROR <span class="token operator">=</span> <span class="token number">401</span><span class="token punctuation">,</span> JSON_PARSE_ERROR <span class="token operator">=</span> <span class="token number">402</span><span class="token punctuation">,</span> CHECKSUM_MISMATCH <span class="token operator">=</span> <span class="token number">403</span><span class="token punctuation">,</span>

    <span class="token comment">// Function Code Processing errors (500-599)</span>
    INVALID_FUNCTION_CODE <span class="token operator">=</span> <span class="token number">500</span><span class="token punctuation">,</span> FUNCTION_CODE_MISMATCH <span class="token operator">=</span> <span class="token number">501</span><span class="token punctuation">,</span> RESPONSE_TYPE_ERROR <span class="token operator">=</span> <span class="token number">502</span><span class="token punctuation">,</span> API_CATEGORY_ERROR <span class="token operator">=</span> <span class="token number">503</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>Error Handling:</strong></p>
<p>The error handling system integrates FTDI VCP driver errors with ZES protocol errors. Errors are categorized as:</p>
<ul>
<li><strong>Transient errors</strong> (e.g., <code>TIMEOUT_ERROR</code>, <code>CRC_ERROR</code>, <code>DEVICE_BUSY</code>) may succeed on retry</li>
<li><strong>Permanent errors</strong> (e.g., <code>INVALID_PARAMETER</code>, <code>DEVICE_NOT_FOUND</code>) require user intervention</li>
</ul>
<h4 id="401-error-handling-with-retry-logic">4.0.1 Error Handling with Retry Logic </h4>
<p>For transient errors, applications can implement retry logic with configurable delays and maximum retry counts. The driver also provides error callback registration for asynchronous error notification.</p>
<h3 id="41-common-structures">4.1 Common Structures </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Structure for device information</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">DeviceInfo</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string port<span class="token punctuation">;</span>       <span class="token comment">// COM port name</span>
    std<span class="token double-colon punctuation">::</span>string description<span class="token punctuation">;</span><span class="token comment">// Device description</span>
    std<span class="token double-colon punctuation">::</span>string serialNumber<span class="token punctuation">;</span><span class="token comment">// Serial number (if available)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Hardware status structure</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">HardwareStatus</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-bool">bool</span> isConnected<span class="token punctuation">;</span>           <span class="token comment">// Connection status</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> ftdiChipStatus<span class="token punctuation">;</span>    <span class="token comment">// FTDI chip status flags</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> bufferOverflows<span class="token punctuation">;</span>   <span class="token comment">// Number of buffer overflow events</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> crcErrors<span class="token punctuation">;</span>         <span class="token comment">// Total CRC errors detected</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> timeoutErrors<span class="token punctuation">;</span>     <span class="token comment">// Total timeout errors</span>
    <span class="token keyword keyword-double">double</span> signalStrength<span class="token punctuation">;</span>      <span class="token comment">// RS485 signal strength (if available)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> framesSent<span class="token punctuation">;</span>        <span class="token comment">// Total frames transmitted</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> framesReceived<span class="token punctuation">;</span>    <span class="token comment">// Total frames received successfully</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Performance metrics structure</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">PerformanceMetrics</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-double">double</span> avgLatencyMs<span class="token punctuation">;</span>        <span class="token comment">// Average response latency</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> bytesPerSecond<span class="token punctuation">;</span>    <span class="token comment">// Throughput in bytes per second</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> successfulFrames<span class="token punctuation">;</span>  <span class="token comment">// Successful frame transmissions</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> failedFrames<span class="token punctuation">;</span>      <span class="token comment">// Failed frame transmissions</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> retryCount<span class="token punctuation">;</span>        <span class="token comment">// Total retries performed</span>
    <span class="token keyword keyword-double">double</span> frameSuccessRate<span class="token punctuation">;</span>    <span class="token comment">// Success rate percentage</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Line status structure</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">LineStatus</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-bool">bool</span> carrierDetect<span class="token punctuation">;</span>         <span class="token comment">// CD signal status</span>
    <span class="token keyword keyword-bool">bool</span> dataSetReady<span class="token punctuation">;</span>          <span class="token comment">// DSR signal status</span>
    <span class="token keyword keyword-bool">bool</span> clearToSend<span class="token punctuation">;</span>           <span class="token comment">// CTS signal status</span>
    <span class="token keyword keyword-bool">bool</span> dataTerminalReady<span class="token punctuation">;</span>     <span class="token comment">// DTR signal status</span>
    <span class="token keyword keyword-bool">bool</span> requestToSend<span class="token punctuation">;</span>         <span class="token comment">// RTS signal status</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> signalStrength<span class="token punctuation">;</span>    <span class="token comment">// Signal strength (0-100%)</span>
    <span class="token keyword keyword-bool">bool</span> busIdle<span class="token punctuation">;</span>               <span class="token comment">// RS485 bus idle status</span>
    <span class="token keyword keyword-bool">bool</span> hardwareError<span class="token punctuation">;</span>         <span class="token comment">// Hardware error detected</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

</code></pre><h3 id="42-windows-driver-implementation-details">4.2 Windows Driver Implementation Details </h3>
<p>The RS485 driver is implemented as a UMDF Filter Driver using Windows Driver Framework components for buffer management, non-blocking I/O, frame processing, and thread synchronization.</p>
<p><strong>Driver Installation Files:</strong></p>
<ul>
<li><strong>RS485Driver.inf</strong>: Windows Information File for driver installation</li>
<li><strong>RS485Driver.dll</strong>: User-mode driver binary</li>
<li><strong>RS485DriverInterface.lib</strong>: Application interface library</li>
<li><strong>RS485DriverInterface.h</strong>: Header file for application development</li>
</ul>
<h4 id="422-device-enumeration">4.2.2 Device Enumeration </h4>
<p>The driver provides device enumeration through Windows Device Manager APIs, supporting Windows 10/11 with native device integration and metadata access.</p>
<h4 id="423-windows-driver-development-and-deployment">4.2.3 Windows Driver Development and Deployment </h4>
<p><strong>Development Environment:</strong> Visual Studio 2022 with Windows Driver Kit (WDK) for Windows 10/11</p>
<p><strong>Driver Installation Process:</strong> Development testing, test signing, production signing, and Device Manager integration</p>
<p>The filter driver is installed as an Upper Filter above the existing FTDI VCP driver, maintaining compatibility with existing FTDI driver infrastructure.</p>
<h4 id="424-buffer-flag-management-and-fifo-guarantee">4.2.4 Buffer Flag Management and FIFO Guarantee </h4>
<p>The RS485 driver implements buffer flag checking to prevent data loss and ensures strict First-In-First-Out ordering. The entire communication protocol revolves around efficient handling of 12-byte payload data.</p>
<h3 id="43-error-handle-api">4.3 Error Handle API </h3>
<p>The Error Handle API provides detailed error information and handling capabilities, including COM port errors inherited from the FTDI driver.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span> <span class="token function">getErrorString</span><span class="token punctuation">(</span>RS485Error error<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span>
</code></pre><p><strong>Purpose:</strong> Get a human-readable description of an error code<br>
<strong>Features:</strong> Comprehensive error coverage, detailed descriptions, internationalization support</p>
<h3 id="44-windows-driver-connection-management">4.4 Windows Driver Connection Management </h3>
<h4 id="441-constructor">4.4.1 Constructor </h4>
<p>Creates a new instance of the RS485 driver interface for Windows driver communication (Windows 10/11).</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token function">AI_SLDAP_RS485_DriverInterface</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
</code></pre><p>The constructor initializes the driver interface and manages payload buffer allocation (5 uplink × 12 bytes + 10 downlink × 12 bytes).</p>
<h4 id="442-open">4.4.2 open </h4>
<p>Opens the RS485 connection through Windows driver interface.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ConnectionResult <span class="token function">open</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>wstring<span class="token operator">&amp;</span> devicePath<span class="token punctuation">)</span>
</code></pre><p><strong>Parameters:</strong> devicePath - Windows device path (obtained from device enumeration)<br>
<strong>Return Value:</strong> ConnectionResult::SUCCESS if successful, otherwise an error code</p>
<h4 id="443-close">4.4.3 close </h4>
<p>Closes the RS485 driver connection.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
</code></pre><p><strong>Parameters:</strong> None<br>
<strong>Return Value:</strong> None<br>
<strong>Remarks:</strong> Releases driver resources and clears remaining payload data in buffers.</p>
<h4 id="444-isopen">4.4.4 isOpen </h4>
<p>Checks if the driver connection is open.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-bool">bool</span> <span class="token function">isOpen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span>
</code></pre><p><strong>Return Value:</strong> true if the driver connection is open, false otherwise</p>
<h4 id="445-buffer-status-monitoring">4.4.5 Buffer Status Monitoring </h4>
<p>Monitor the driver-managed buffer status for both uplink and downlink communications.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>BufferResult <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>BufferStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span>
</code></pre><p><strong>Parameters:</strong> status - Reference to BufferStatus structure<br>
<strong>Return Value:</strong> BufferResult::SUCCESS if successful, otherwise an error code</p>
<h4 id="446-deviceiocontrol-implementation">4.4.6 DeviceIoControl() Implementation </h4>
<p>The high-level API functions internally use DeviceIoControl() with buffer flag checking and FIFO guarantee.</p>
<p><strong>Key Implementation Points:</strong></p>
<ul>
<li>DeviceIoControl() is encapsulated within API functions</li>
<li>Buffer flag checking prevents overflow</li>
<li>12-byte payload focus for core protocol information</li>
<li>FIFO guarantee with strict ordering</li>
<li>Comprehensive error handling and thread safety</li>
</ul>
<h3 id="45-master-broadcasting-api-system-configuration-commands">4.5 Master Broadcasting API (System Configuration Commands) </h3>
<p>The Master Broadcasting API enables PC applications to send broadcast frames to slave devices for system-level configuration (S-series commands).</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ConfigurationResult <span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span>
</code></pre><p><strong>Purpose:</strong> Configure system parameters via broadcasting (S-series commands only)<br>
<strong>Parameters:</strong> commandKey ("S001", "S002"), value (S001: 1-31, S002: baud rates)<br>
<strong>Return Value:</strong> ConfigurationResult indicating success or specific error</p>
<p><strong>Supported Commands:</strong></p>
<ul>
<li><strong>S001</strong>: Set RS485 slave address (1-31)</li>
<li><strong>S002</strong>: Set baud rate (9600, 19200, 38400, 57600, 115200)</li>
</ul>
<h4 id="452-implementation-details">4.5.2 Implementation Details </h4>
<p><strong>Hardware Requirements:</strong></p>
<ul>
<li>Single slave device must be connected to RS485 bus during broadcast operations</li>
<li>Multiple slaves cause bus collisions during acknowledgment</li>
<li>Broadcast frames sent to address 0x00 with special acknowledgment mechanism</li>
</ul>
<p><strong>Key Features:</strong></p>
<ul>
<li>Targeted communication to specific slave device</li>
<li>Mandatory acknowledgment mechanism</li>
<li>Hardware limitation enforcement</li>
<li>Automatic frame construction and retry handling</li>
</ul>
<h3 id="46-master-assign-data-api">4.6 Master Assign Data API </h3>
<p>The Master Assign Data API assigns data to slave devices, including user configuration parameters (U-series) and AI model weights and bias data (W-series).</p>
<h4 id="461-user-configuration-commands-u-series">4.6.1 User Configuration Commands (U-series) </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ConfigurationResult <span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span>
</code></pre><p><strong>Purpose:</strong> Configure user parameters on slave devices<br>
<strong>Parameters:</strong> commandKey (e.g., "U001", "U002"), value (8-byte value)<br>
<strong>Return Value:</strong> ConfigurationResult indicating success or error</p>
<p><strong>Addressing:</strong> Uses slave address previously set by S001 command. Set S001 before using U-series commands to avoid broadcasting to all slaves.</p>
<p><strong>Supported Commands:</strong></p>
<ul>
<li><strong>U001</strong>: SEL detection threshold (40-500 mA)</li>
<li><strong>U002</strong>: SEL maximum amplitude threshold (1000-2000 mA)</li>
<li><strong>U003</strong>: SEL detections before power cycle (1-5)</li>
<li><strong>U004</strong>: Power cycle duration (200, 400, 600, 800, 1000 ms)</li>
<li><strong>U005</strong>: GPIO input functions (channel/enable)</li>
<li><strong>U006</strong>: GPIO output functions (channel/enable)</li>
</ul>
<h4 id="462-configuration-verification">4.6.2 Configuration Verification </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>VerificationResult <span class="token function">verifySystemConfig</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> expectedValue<span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isMatching<span class="token punctuation">)</span>
</code></pre><p><strong>Purpose:</strong> Verify that a configuration value matches the expected value<br>
<strong>Parameters:</strong> commandKey, expectedValue, isMatching (reference to boolean result)<br>
<strong>Return Value:</strong> VerificationResult indicating success or error</p>
<h4 id="463-ai-model-weight-and-bias-data-commands-w-series">4.6.3 AI Model Weight and Bias Data Commands (W-series) </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ModelDataResult <span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span> address<span class="token punctuation">,</span> std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> data<span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span> isWrite<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> length <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">)</span>
</code></pre><p><strong>Purpose:</strong> Manage AI model weights and bias data stored in FRAM memory<br>
<strong>Parameters:</strong> slaveAddress (1-31), address (FRAM memory address), data (vector), isWrite (operation type), length (for reads)<br>
<strong>Return Value:</strong> ModelDataResult indicating success or error</p>
<p><strong>Supported Commands:</strong></p>
<ul>
<li><strong>W001</strong>: Write model data to FRAM</li>
<li><strong>W002</strong>: Read model data from FRAM</li>
</ul>
<h3 id="47-master-request-api">4.7 Master Request API </h3>
<p>The Master Request API enables PC applications to send requests to slave devices and receive responses without blocking the application thread.</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>RequestResult <span class="token function">requestData</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> dataKey<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> RequestOptions<span class="token operator">*</span> options <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span>
</code></pre><p><strong>Purpose:</strong> Request information from slave devices<br>
<strong>Parameters:</strong> dataKey (A-series commands), options (filtering, pagination)<br>
<strong>Return Value:</strong> RequestResult indicating success or error</p>
<p><strong>Key Features:</strong></p>
<ul>
<li>Non-blocking design with immediate return after acknowledgment</li>
<li>Asynchronous operation using OS threads</li>
<li>Data retrieval separation using receiveSlaveResponse function</li>
<li>Parameterized requests with filtering options</li>
</ul>
<p><strong>RequestOptions Structure:</strong> Supports time filtering, pagination, event type filtering, and format options for A001/A004 JSON responses.</p>
<h4 id="472-optional-callback-mechanism">4.7.2 Optional Callback Mechanism </h4>
<p>The driver provides optional callback mechanism for notification when response data is available, eliminating the need to poll using <code>receiveSlaveResponse</code>.</p>
<h3 id="48-slave-response-api">4.8 Slave Response API </h3>
<p>The Slave Response API enables PC applications to receive data from slave devices using a two-phase non-blocking approach.</p>
<p><strong>Phase 1: Check Data Availability</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ResponseResult <span class="token function">checkSlaveDataReady</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isDataReady<span class="token punctuation">)</span>
</code></pre><p><strong>Phase 2: Retrieve Response Data</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>ResponseResult <span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> timeout <span class="token operator">=</span> <span class="token number">100</span><span class="token punctuation">)</span>
</code></pre><p><strong>Design Benefits:</strong></p>
<ul>
<li>No thread blocking for responsive applications</li>
<li>Efficient multi-slave polling</li>
<li>Predictable memory with fixed-size arrays</li>
<li>Real-time compatible for airborne applications</li>
</ul>
<p><strong>Key Features:</strong></p>
<ul>
<li>FIFO buffer system prevents data loss</li>
<li>Data ready notification system</li>
<li>Multi-frame response handling</li>
<li>Intelligent data alignment for different response sizes
<ul>
<li>Driver reassembles the complete response automatically</li>
</ul>
</li>
</ul>
<p><strong>Implementation Notes:</strong></p>
<ul>
<li>Automatic buffer management with overflow protection</li>
<li>Multi-frame responses reassembled transparently</li>
<li>Frame sequencing handled automatically by driver</li>
</ul>
<p><strong>Response Handling Approaches:</strong></p>
<p>The API supports two main approaches:</p>
<ol>
<li><strong>Callback-Based Approach</strong>: Event-driven, best for real-time monitoring and GUI applications</li>
<li><strong>Polling-Based Approach</strong>: Sequential, best for simple scripts and ordered data processing</li>
<li><strong>Blocking Approach</strong>: Not recommended for airborne systems</li>
</ol>
<h4 id="482-buffer-configuration-and-management">4.8.2 Buffer Configuration and Management </h4>
<p>The driver provides comprehensive buffer management capabilities for airborne environments.</p>
<p><strong>Buffer Configuration:</strong></p>
<ul>
<li>Default 256KB buffer for typical applications</li>
<li>512KB buffer for high data volume or slow polling</li>
<li>Configurable during driver initialization</li>
</ul>
<p><strong>Buffer Management API:</strong></p>
<ul>
<li>Buffer size and usage monitoring</li>
<li>Per-slave pending response counts</li>
<li>Buffer clearing and threshold management</li>
<li>Overflow policies (discard oldest/newest, trigger error)</li>
<li>FIFO buffer system with thread-safe implementation</li>
</ul>
<h4 id="483-thread-safety">4.8.3 Thread Safety </h4>
<p>The Slave Response API is thread-safe, allowing multiple threads to call <code>receiveSlaveResponse</code> concurrently. Callbacks are invoked on separate threads, so callback handlers should be thread-safe.</p>
<h4 id="484-relationship-with-master-request-api">4.8.4 Relationship with Master Request API </h4>
<p>The Slave Response API works with the Master Request API to form a complete non-blocking communication system:</p>
<ul>
<li>Request-response pairing with data retrieval separation</li>
<li>Non-blocking design for airborne environments</li>
<li>Asynchronous operation with background OS threads</li>
<li>Buffer management for response storage</li>
<li>Callback alternative for event-driven approach</li>
</ul>
<h2 id="5-using-the-api">5. Using the API </h2>
<h3 id="51-supported-operating-systems-and-api-consistency">5.1 Supported Operating Systems and API Consistency </h3>
<p><strong>Cross-Platform Support:</strong></p>
<ul>
<li><strong>Windows</strong> (10, 11) - Windows Driver Kit (WDK) implementation</li>
<li><strong>Linux</strong> (Ubuntu 18.04+, CentOS 7+, RHEL 8+) - Linux kernel module implementation</li>
</ul>
<p><strong>API Design Consistency:</strong><br>
The RS485 driver API is identical across Windows and Linux platforms with consistent interface, data format, error handling, and buffer management. Platform differences are limited to device paths and installation methods.</p>
<h3 id="511-platform-differences">5.1.1 Platform Differences </h3>
<p><strong>Key Differences:</strong></p>
<ul>
<li><strong>Device Paths</strong>: Windows uses COM ports (<code>\\.\COM3</code>), Linux uses <code>/dev/ttyUSB0</code></li>
<li><strong>Installation</strong>: Windows uses INF files, Linux uses kernel modules</li>
<li><strong>Permissions</strong>: Windows requires administrator, Linux requires <code>dialout</code> group membership</li>
<li><strong>Implementation</strong>: Windows uses DeviceIoControl(), Linux uses ioctl() system calls</li>
</ul>
<h3 id="512-cross-platform-data-format-standardization">5.1.2 Cross-Platform Data Format Standardization </h3>
<p><strong>Universal Data Compatibility:</strong><br>
The 12-byte payload format uses standardized data representation:</p>
<ul>
<li><strong>Integer Data</strong>: 32-bit little-endian format</li>
<li><strong>Floating-Point</strong>: IEEE 754 standard (universally supported)</li>
<li><strong>Endianness</strong>: Little-endian format (x86/x64 native)</li>
<li><strong>Cross-Language</strong>: Consistent encoding across C++, Python, Java</li>
</ul>
<h3 id="52-installation">5.2 Installation </h3>
<h4 id="521-ftdi-vcp-driver-installation">5.2.1 FTDI VCP Driver Installation </h4>
<ol>
<li>Download driver from <a href="https://ftdichip.com/drivers/vcp-drivers/">https://ftdichip.com/drivers/vcp-drivers/</a></li>
<li>Install according to OS instructions</li>
<li>Connect USB-RS485-WE-1800-BT adapter</li>
<li>Verify device appears as virtual COM port</li>
</ol>
<h4 id="522-zes-driver-installation-options">5.2.2 ZES Driver Installation Options </h4>
<ul>
<li><strong>Option 1</strong>: OS I/O Thread Integration (device driver)</li>
<li><strong>Option 2</strong>: Independent Thread Operation (background service) - <strong>Recommended</strong></li>
</ul>
<h4 id="523-zes-driver-requirements">5.2.3 ZES Driver Requirements </h4>
<p>Core functionalities include data link layer implementation, RS485 bus control, transmission error handling, buffer and FIFO functions, and five-category API interface with thread-safe non-blocking design.</p>
<h3 id="53-windows-driver-quick-start-guide">5.3 Windows Driver Quick Start Guide </h3>
<h4 id="531-basic-usage-steps">5.3.1 Basic Usage Steps </h4>
<ol>
<li><strong>Enumerate Devices</strong>: Use <code>enumerateDevices()</code> to find available RS485 devices</li>
<li><strong>Connect</strong>: Open connection using <code>open(devicePath)</code></li>
<li><strong>Configure System</strong>: Set slave address using <code>configureSystemSettings("S001", address)</code></li>
<li><strong>Configure User Settings</strong>: Set parameters using <code>configureUserSettings()</code></li>
<li><strong>Request Data</strong>: Use <code>requestData()</code> for A-series commands</li>
<li><strong>Receive Responses</strong>: Use <code>receiveSlaveResponse()</code> or register callbacks</li>
<li><strong>Clean Up</strong>: Close connection using <code>close()</code></li>
</ol>
<h4 id="532-error-handling">5.3.2 Error Handling </h4>
<ul>
<li>Register error callbacks for transient vs. permanent error categorization</li>
<li>Configure buffer overflow policies (discard oldest/newest, trigger error)</li>
<li>Monitor buffer status to prevent overflow</li>
</ul>
<h3 id="53-api-usage-examples">5.3 API Usage Examples </h3>
<h4 id="531-basic-usage-pattern">5.3.1 Basic Usage Pattern </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 1. Enumerate and connect</span>
std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DeviceInfo<span class="token operator">&gt;</span> devices<span class="token punctuation">;</span>
<span class="token class-name">AI_SLDAP_RS485_Driver</span><span class="token double-colon punctuation">::</span><span class="token function">enumerateDevices</span><span class="token punctuation">(</span>devices<span class="token punctuation">)</span><span class="token punctuation">;</span>
AI_SLDAP_RS485_Driver <span class="token function">driver</span><span class="token punctuation">(</span>devices<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">.</span>port<span class="token punctuation">)</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">open</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 2. Configure system (S-series)</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> <span class="token number">5</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Set slave address</span>

<span class="token comment">// 3. Configure user settings (U-series)</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// SEL threshold</span>

<span class="token comment">// 4. Request data (A-series)</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A001"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// SEL event log</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">200</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 5. Clean up</span>
driver<span class="token punctuation">.</span><span class="token function">close</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h4 id="532-error-handling-1">5.3.2 Error Handling </h4>
<p>The API provides comprehensive error handling with specific result types for different operations and categorization of transient vs. permanent errors.</p>
<h2 id="6-additional-features">6. Additional Features </h2>
<h3 id="61-performance-metrics-and-optimization">6.1 Performance Metrics and Optimization </h3>
<p>The API provides performance monitoring capabilities including:</p>
<p><strong>PerformanceMetrics Structure:</strong></p>
<ul>
<li>Average latency, throughput, success rates</li>
<li>CRC errors, timeouts, buffer overflow counts</li>
<li>Bus utilization and response time analysis</li>
</ul>
<p><strong>LineStatus Structure:</strong></p>
<ul>
<li>Signal strength, noise level, hardware error detection</li>
<li>Bus idle status and last activity monitoring</li>
</ul>
<p><strong>Performance Optimization Guidelines:</strong></p>
<ul>
<li>Monitor frame success rates (target: &gt;95%)</li>
<li>Use higher baud rates for shorter cable runs</li>
<li>Consider larger buffers for high-volume transfers</li>
</ul>
<h3 id="62-hardware-features">6.2 Hardware Features </h3>
<table>
<thead>
<tr>
<th>Feature</th>
<th>Command</th>
<th>Description</th>
<th>API Call Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>SEL Protection</strong></td>
<td>U002, U003</td>
<td>Configurable amplitude threshold (1000-2000mA) and detection count (1-5)</td>
<td><code>configureUserSettings('U002', 1500)</code></td>
</tr>
<tr>
<td><strong>GPIO Inputs</strong></td>
<td>U005</td>
<td>2 channels that can pause power cycling when high (disabled by default)</td>
<td><code>configureUserSettings('U005', 0x100000000ULL)</code></td>
</tr>
<tr>
<td><strong>GPIO Outputs</strong></td>
<td>U006</td>
<td>2 channels that go high during power cycling (disabled by default)</td>
<td><code>configureUserSettings('U006', 0x100000001ULL)</code></td>
</tr>
</tbody>
</table>
<h3 id="63-gui-integration">6.3 GUI Integration </h3>
<p>The RS485 driver includes a GUI application for easy configuration:</p>
<ul>
<li>Visual interface for all system configuration parameters</li>
<li>Real-time status monitoring</li>
<li>Device discovery and connection management</li>
</ul>
<h2 id="7-windows-driver-implementation-summary">7. Windows Driver Implementation Summary </h2>
<h3 id="71-key-changes-filter-driver-architecture">7.1 Key Changes: Filter Driver Architecture </h3>
<p><strong>Architecture Changes:</strong></p>
<ol>
<li><strong>Driver Type</strong>: Implemented as UMDF 2.0 <strong>Filter Driver</strong> (not replacing FTDI VCP)</li>
<li><strong>Driver Stack Position</strong>: <strong>Upper Filter</strong> above FTDI VCP Function Driver</li>
<li><strong>Buffer Management</strong>: Implemented driver-managed fixed-size payload buffers (5 uplink × 12 bytes + 10 downlink × 12 bytes)</li>
<li><strong>Protocol Processing</strong>: Added RS485/ZES protocol intelligence on top of basic serial communication</li>
<li><strong>Communication Method</strong>: IOCTL-based high-level API while leveraging FTDI VCP for hardware communication</li>
</ol>
<p><strong>Technical Benefits:</strong></p>
<ul>
<li><strong>Leverages Existing Infrastructure</strong>: Uses proven FTDI VCP drivers as foundation</li>
<li><strong>Enhanced Functionality</strong>: Adds RS485 protocol processing and intelligent buffering</li>
<li><strong>Maintains Compatibility</strong>: Existing FTDI driver infrastructure remains intact</li>
<li><strong>Improved Debugging</strong>: Visual Studio integration with WDK debugging tools</li>
<li><strong>Professional Deployment</strong>: Standard Windows filter driver installation and signing process</li>
</ul>
<p><strong>Buffer Management Advantages:</strong></p>
<ul>
<li><strong>Fixed Memory Allocation</strong>: Eliminates dynamic memory allocation overhead</li>
<li><strong>Real-Time Performance</strong>: Optimized for low-latency airborne environments</li>
<li><strong>Thread-Safe Operations</strong>: Built-in synchronization for concurrent access</li>
<li><strong>Overflow Protection</strong>: Configurable policies for buffer overflow handling</li>
</ul>
<h3 id="72-application-deliverable">7.2 Application Deliverable </h3>
<p><strong>Final Deliverable:</strong></p>
<p><strong>RS485_Communication_Application.exe</strong>: A complete Windows application that provides:</p>
<ol>
<li><strong>Integrated FTDI VCP Driver Functionality</strong>: Built-in support for USB-RS485-WE-1800-BT converter without requiring separate FTDI driver installation</li>
<li><strong>ZES Protocol Implementation</strong>: Complete implementation of the ZES proprietary data link layer protocol as specified in the guidance document</li>
<li><strong>User-Mode Driver Framework (UMDF 2)</strong>: Windows-native driver implementation for reliable RS485 communication</li>
<li><strong>Advanced Buffer Management</strong>: Driver-managed frame buffers with intelligent overflow handling</li>
<li><strong>High-Level API Interface</strong>: Five categories of APIs that abstract away protocol complexity</li>
<li><strong>Error Handling and Recovery</strong>: Comprehensive error management with automatic retry mechanisms</li>
<li><strong>Configuration Management</strong>: Persistent storage of device configurations in FRAM</li>
<li><strong>Multi-Device Support</strong>: Support for up to 30 slave devices on a single RS485 bus</li>
</ol>
<p><strong>Architecture Overview:</strong><br>
The application combines Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality to create a single executable that handles all aspects of RS485 communication with AI-SLDAP devices. This approach eliminates the need for separate driver installations while providing enterprise-grade reliability and performance.<br>
5. <strong>Test Applications</strong>: Comprehensive test suite for validation<br>
6. <strong>Documentation</strong>: Updated implementation guides and API documentation</p>
<h3 id="73-migration-benefits-summary">7.3 Migration Benefits Summary </h3>
<p><strong>For Developers:</strong></p>
<ul>
<li>Simplified installation process (no third-party driver dependencies)</li>
<li>Enhanced debugging capabilities with Visual Studio WDK integration</li>
<li>Better error handling and diagnostics through Windows driver framework</li>
<li>Professional driver signing and deployment process</li>
</ul>
<p><strong>For End Users:</strong></p>
<ul>
<li>Automatic driver installation through Windows Update (when signed)</li>
<li>Better system stability and reliability</li>
<li>Native Windows Device Manager integration</li>
<li>Improved performance and lower latency</li>
</ul>
<p><strong>For System Integration:</strong></p>
<ul>
<li>Standardized Windows driver architecture</li>
<li>Better compatibility with Windows security policies</li>
<li>Enhanced support for enterprise deployment scenarios</li>
<li>Future-proof design aligned with Windows driver development trends</li>
</ul>
<h2 id="9-critical-technical-implementation-highlights">9. Critical Technical Implementation Highlights </h2>
<h3 id="91-non-blocking-driver-operation-never-hold-user-threads">9.1 Non-Blocking Driver Operation: Never Hold User Threads </h3>
<p><strong>Problem Statement:</strong><br>
How does the driver ensure that user application threads are never blocked during RS485 operations, especially in real-time airborne environments?</p>
<p><strong>Solution Architecture:</strong></p>
<p><strong>1. Asynchronous I/O Completion Model</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// User thread makes IOCTL call</span>
NTSTATUS <span class="token function">ProcessIOCTLRequest</span><span class="token punctuation">(</span>IWDFIoRequest<span class="token operator">*</span> pRequest<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// NEVER block here - immediately queue request and return</span>
    NTSTATUS status <span class="token operator">=</span> <span class="token function">QueueRequestForAsyncProcessing</span><span class="token punctuation">(</span>pRequest<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">NT_SUCCESS</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Return STATUS_PENDING - user thread continues immediately</span>
        <span class="token keyword keyword-return">return</span> STATUS_PENDING<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Only return synchronously for immediate errors</span>
    <span class="token keyword keyword-return">return</span> status<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Processing happens in driver-managed work items</span>
<span class="token keyword keyword-void">void</span> <span class="token function">ProcessRequestAsync</span><span class="token punctuation">(</span>IWDFIoRequest<span class="token operator">*</span> pRequest<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// This runs in driver context, not user thread</span>
    <span class="token comment">// Perform actual RS485 communication</span>
    <span class="token comment">// Complete request when done</span>
    pRequest<span class="token operator">-&gt;</span><span class="token function">Complete</span><span class="token punctuation">(</span>STATUS_SUCCESS<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>2. Driver-Managed Thread Pool</strong></p>
<ul>
<li><strong>Work Items</strong>: All complex processing occurs in driver work items</li>
<li><strong>DPC Context</strong>: Frame parsing happens at DPC level for minimal latency</li>
<li><strong>Completion Callbacks</strong>: User applications are notified via completion callbacks</li>
<li><strong>No Blocking Primitives</strong>: Driver never uses blocking waits or sleeps</li>
</ul>
<p><strong>3. Request Queuing Strategy</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">RequestManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    WDFQUEUE m_pendingRequests<span class="token punctuation">;</span>     <span class="token comment">// Queue for user requests</span>
    WDFWORKITEM m_processorWorkItems<span class="token punctuation">[</span><span class="token number">4</span><span class="token punctuation">]</span><span class="token punctuation">;</span>  <span class="token comment">// 4 parallel processors</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// User thread context - returns immediately</span>
    NTSTATUS <span class="token function">QueueRequest</span><span class="token punctuation">(</span>IWDFIoRequest<span class="token operator">*</span> pRequest<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token function">WdfIoQueueAdd</span><span class="token punctuation">(</span>m_pendingRequests<span class="token punctuation">,</span> pRequest<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">WdfWorkItemEnqueue</span><span class="token punctuation">(</span>m_processorWorkItems<span class="token punctuation">[</span><span class="token function">GetNextProcessor</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> STATUS_PENDING<span class="token punctuation">;</span>  <span class="token comment">// User thread continues</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Driver work item context - processes requests asynchronously</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">ProcessQueuedRequests</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        IWDFIoRequest<span class="token operator">*</span> pRequest<span class="token punctuation">;</span>
        <span class="token keyword keyword-while">while</span> <span class="token punctuation">(</span><span class="token function">WdfIoQueueRetrieveNextRequest</span><span class="token punctuation">(</span>m_pendingRequests<span class="token punctuation">,</span> <span class="token operator">&amp;</span>pRequest<span class="token punctuation">)</span> <span class="token operator">==</span> STATUS_SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token function">ProcessSingleRequest</span><span class="token punctuation">(</span>pRequest<span class="token punctuation">)</span><span class="token punctuation">;</span>
            pRequest<span class="token operator">-&gt;</span><span class="token function">Complete</span><span class="token punctuation">(</span>STATUS_SUCCESS<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>4. Benefits for Airborne Systems</strong></p>
<ul>
<li><strong>Real-Time Responsiveness</strong>: User applications remain responsive</li>
<li><strong>Deterministic Behavior</strong>: No unpredictable blocking delays</li>
<li><strong>Scalability</strong>: Multiple applications can use driver simultaneously</li>
<li><strong>System Stability</strong>: No thread starvation or deadlock risks</li>
</ul>
<h3 id="92-frame-processing-efficient-state-machine-implementation">9.2 Frame Processing: Efficient State Machine Implementation </h3>
<p><strong>Problem Statement:</strong><br>
How does the driver efficiently process incoming RS485 frames byte-by-byte without blocking and handle frame synchronization, CRC validation, and error recovery?</p>
<p><strong>Solution Architecture:</strong></p>
<p><strong>1. Multi-Level Processing Pipeline</strong></p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>Level 1: DPC Context (Minimal Latency)
├── Byte-by-byte state machine
├── Frame boundary detection
└── Basic validation

Level 2: Work Item Context (Complex Processing)
├── CRC8 validation
├── Protocol interpretation
├── Buffer management
└── Error handling
</code></pre><p><strong>2. State Machine Implementation</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Called from DPC - processes one byte in microseconds</span>
FrameProcessResult <span class="token function">ProcessIncomingByte</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> byte<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// State machine with minimal processing per byte</span>
    <span class="token keyword keyword-switch">switch</span> <span class="token punctuation">(</span>m_currentState<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-case">case</span> WAITING_HEADER<span class="token operator">:</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>byte <span class="token operator">==</span> <span class="token number">0xAA</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                m_frameBuffer<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span> <span class="token operator">=</span> byte<span class="token punctuation">;</span>
                m_bytesReceived <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
                m_currentState <span class="token operator">=</span> READING_ID<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
            <span class="token keyword keyword-return">return</span> CONTINUE<span class="token punctuation">;</span>  <span class="token comment">// Never blocks</span>

        <span class="token keyword keyword-case">case</span> READING_PAYLOAD<span class="token operator">:</span>
            m_frameBuffer<span class="token punctuation">[</span>m_bytesReceived<span class="token operator">++</span><span class="token punctuation">]</span> <span class="token operator">=</span> byte<span class="token punctuation">;</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_bytesReceived <span class="token operator">==</span> <span class="token number">14</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                m_currentState <span class="token operator">=</span> READING_CRC<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
            <span class="token keyword keyword-return">return</span> CONTINUE<span class="token punctuation">;</span>  <span class="token comment">// Process next byte</span>

        <span class="token keyword keyword-case">case</span> READING_TRAILER<span class="token operator">:</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>byte <span class="token operator">==</span> <span class="token number">0x0D</span> <span class="token operator">&amp;&amp;</span> m_bytesReceived <span class="token operator">==</span> <span class="token number">16</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                <span class="token keyword keyword-return">return</span> FRAME_READY<span class="token punctuation">;</span>  <span class="token comment">// Schedule work item</span>
            <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
                <span class="token function">ResetStateMachine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword keyword-return">return</span> FRAME_ERROR<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>3. Frame Validation and Error Recovery</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Called from work item - can perform complex operations</span>
<span class="token keyword keyword-void">void</span> <span class="token function">ProcessCompleteFrame</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// CRC8 validation</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> calculatedCRC <span class="token operator">=</span> <span class="token function">CalculateCRC8</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>frame<span class="token punctuation">.</span>id_byte<span class="token punctuation">,</span> <span class="token number">13</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>calculatedCRC <span class="token operator">!=</span> frame<span class="token punctuation">.</span>crc8<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Non-blocking error recovery</span>
        <span class="token function">ScheduleResendRequest</span><span class="token punctuation">(</span>frame<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Route to appropriate buffer based on function code</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> functionCode <span class="token operator">=</span> <span class="token punctuation">(</span>frame<span class="token punctuation">.</span>id_byte <span class="token operator">&gt;&gt;</span> <span class="token number">5</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0x07</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> deviceAddress <span class="token operator">=</span> frame<span class="token punctuation">.</span>id_byte <span class="token operator">&amp;</span> <span class="token number">0x1F</span><span class="token punctuation">;</span>

    <span class="token comment">// Store in ring buffer and notify waiting applications</span>
    <span class="token function">StoreInDownlinkBuffer</span><span class="token punctuation">(</span>frame<span class="token punctuation">,</span> deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token function">NotifyWaitingApplication</span><span class="token punctuation">(</span>deviceAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Async notification</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>4. Performance Characteristics</strong></p>
<ul>
<li><strong>DPC Processing</strong>: &lt; 10 microseconds per byte</li>
<li><strong>Frame Completion</strong>: &lt; 100 microseconds for complete frame processing</li>
<li><strong>Buffer Design</strong>: Fixed 12-byte payload buffer slots (no frame overhead stored)</li>
<li><strong>Error Recovery</strong>: Automatic retry without user intervention</li>
<li><strong>Throughput</strong>: Supports full RS485 bandwidth (up to 115200 baud)</li>
</ul>
<p><strong>5. Synchronization Strategy</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Lock-free design for maximum performance</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">FrameBuffer</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    <span class="token keyword keyword-volatile">volatile</span> LONG m_writeIndex<span class="token punctuation">;</span>
    <span class="token keyword keyword-volatile">volatile</span> LONG m_readIndex<span class="token punctuation">;</span>
    RS485Frame m_frames<span class="token punctuation">[</span>BUFFER_SIZE<span class="token punctuation">]</span><span class="token punctuation">;</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Producer (DPC context) - lock-free</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">PushFrame</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        LONG currentWrite <span class="token operator">=</span> m_writeIndex<span class="token punctuation">;</span>
        LONG nextWrite <span class="token operator">=</span> <span class="token punctuation">(</span>currentWrite <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">%</span> BUFFER_SIZE<span class="token punctuation">;</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>nextWrite <span class="token operator">==</span> m_readIndex<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> <span class="token boolean">false</span><span class="token punctuation">;</span>  <span class="token comment">// Buffer full</span>
        <span class="token punctuation">}</span>

        m_frames<span class="token punctuation">[</span>currentWrite<span class="token punctuation">]</span> <span class="token operator">=</span> frame<span class="token punctuation">;</span>
        <span class="token function">InterlockedExchange</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>m_writeIndex<span class="token punctuation">,</span> nextWrite<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Consumer (user context) - lock-free</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">PopFrame</span><span class="token punctuation">(</span>RS485Frame<span class="token operator">&amp;</span> frame<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        LONG currentRead <span class="token operator">=</span> m_readIndex<span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>currentRead <span class="token operator">==</span> m_writeIndex<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-return">return</span> <span class="token boolean">false</span><span class="token punctuation">;</span>  <span class="token comment">// Buffer empty</span>
        <span class="token punctuation">}</span>

        frame <span class="token operator">=</span> m_frames<span class="token punctuation">[</span>currentRead<span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token function">InterlockedExchange</span><span class="token punctuation">(</span><span class="token operator">&amp;</span>m_readIndex<span class="token punctuation">,</span> <span class="token punctuation">(</span>currentRead <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">%</span> BUFFER_SIZE<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>Key Technical Features:</strong></p>
<ol>
<li><strong>Zero User Thread Blocking</strong>: All operations return immediately or use async completion</li>
<li><strong>Frame Processing</strong>: State machine processes frames with minimal CPU overhead</li>
<li><strong>Lock-Free Design</strong>: Ring buffers use atomic operations instead of locks</li>
<li><strong>Automatic Error Recovery</strong>: CRC errors and timeouts handled transparently</li>
<li><strong>Real-Time Capability</strong>: Suitable for airborne and industrial control systems</li>
</ol>
<h2 id="8-frequently-asked-questions-faq">8. Frequently Asked Questions (FAQ) </h2>
<h3 id="81-windows-driver-questions">8.1 Windows Driver Questions </h3>
<p><strong>Q: What are the system requirements for the Windows Driver Kit (WDK) based RS485 driver?</strong><br>
A: The driver requires Windows 10 or later (64-bit), Visual Studio 2022 with WDK extension for development, and appropriate USB-RS485 converter hardware. The driver is digitally signed for production deployment.</p>
<p><strong>Q: How does the driver-managed buffer system work?</strong><br>
A: The driver maintains fixed-size payload buffers: 5 payload slots (60 bytes) for uplink (PC to device) and 10 payload slots (120 bytes) for downlink (device to PC). Each payload slot stores exactly 12 bytes of meaningful data. The driver automatically manages these buffers with configurable overflow policies.</p>
<p><strong>Q: Can I use the old FTDI VCP driver alongside the new Windows filter driver?</strong><br>
A: Yes! In fact, our RS485 filter driver <strong>requires</strong> the FTDI VCP driver to be present. The FTDI VCP driver serves as the Function Driver (lower layer) that handles the basic USB-to-serial conversion, while our RS485 filter driver sits above it as an Upper Filter Driver (upper layer) to provide RS485 protocol processing and advanced buffering.</p>
<p><strong>Q: How do I install the Windows driver?</strong><br>
A: For development: Use Visual Studio's driver deployment features. For production: Install the signed driver package (.inf file) through Device Manager or use the provided installer. The driver will appear in Windows Device Manager under "Ports" or a custom device category.</p>
<p><strong>Q: What happens if the driver buffer overflows?</strong><br>
A: The driver supports three overflow policies: DISCARD_OLDEST (default), DISCARD_NEWEST, or TRIGGER_ERROR. Applications can monitor buffer status and configure the policy based on their requirements.</p>
<p><strong>Q: How does the Filter Driver communicate with the FTDI VCP Function Driver?</strong><br>
A: The RS485 Filter Driver sits in the Windows driver stack above the FTDI VCP Function Driver. When applications send IOCTL requests to our filter driver, it processes the RS485 protocol logic (frame packing, CRC calculation, etc.) and then forwards the processed serial data to the FTDI VCP driver using standard Windows I/O mechanisms (IRP forwarding). Similarly, data received from the FTDI driver is processed by our filter driver before being returned to the application.</p>
<p><strong>Q: How does the driver ensure it never blocks user application threads?</strong><br>
A: The driver uses a sophisticated asynchronous I/O model where all IOCTL calls return immediately with STATUS_PENDING. Actual processing occurs in driver-managed work items and DPC contexts. User threads never wait for RS485 operations to complete - instead, they receive completion notifications through callbacks or can poll for results. This is critical for real-time airborne systems where thread blocking is unacceptable.</p>
<p><strong>Q: How does the driver process RS485 frames efficiently?</strong><br>
A: The driver implements a multi-level frame processing pipeline: (1) DPC context handles byte-by-byte state machine processing in microseconds, detecting frame boundaries and basic validation; (2) Work item context performs complex operations like CRC8 validation, protocol interpretation, and buffer management. The state machine processes one byte at a time without blocking, using lock-free ring buffers for maximum performance. This design supports full RS485 bandwidth while maintaining real-time responsiveness.</p>
<h3 id="82-cross-platform-and-data-format-questions">8.2 Cross-Platform and Data Format Questions </h3>
<p><strong>Q: Is the API design consistent with typical Linux system APIs?</strong><br>
A: Yes, the RS485 driver API follows standard Linux patterns:</p>
<ul>
<li><strong>Device Enumeration</strong>: Similar to <code>/dev/ttyUSB*</code> enumeration used by other serial libraries</li>
<li><strong>Error Handling</strong>: Uses standard error code patterns similar to Linux system calls</li>
<li><strong>Threading</strong>: Compatible with pthread and standard Linux threading models</li>
<li><strong>Permissions</strong>: Follows standard Linux device permission models (dialout group)</li>
<li><strong>Installation</strong>: Uses standard kernel module installation procedures</li>
</ul>
<p>The high-level API functions are identical between Windows and Linux, with only platform-specific implementation details differing (device paths, internal system calls).</p>
<p><strong>Q: How does the data format ensure compatibility across different systems and programming languages?</strong><br>
A: The API uses universal standards to ensure cross-platform compatibility:</p>
<ol>
<li><strong>Byte Order</strong>: Little-endian format (native to x86/x64, consistent across platforms)</li>
<li><strong>Integer Format</strong>: 32-bit unsigned integers (standard across C++, Python, Java, C#)</li>
<li><strong>Floating-Point</strong>: IEEE 754 standard (universally supported)</li>
<li><strong>Padding</strong>: Explicit zero-padding prevents data corruption</li>
<li><strong>Validation</strong>: Helper functions ensure correct encoding/decoding</li>
</ol>
<p>Example showing identical results across platforms:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// C++ (Windows/Linux):</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> value <span class="token operator">=</span> <span class="token number">1500</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint64_t">uint64_t</span> payload <span class="token operator">=</span> <span class="token class-name">RS485DataFormat</span><span class="token double-colon punctuation">::</span><span class="token function">encodeInteger</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// Result: 0x00000000000005DC</span>

<span class="token comment">// Python (Windows/Linux):</span>
<span class="token keyword keyword-import">import</span> <span class="token keyword keyword-struct">struct</span>
<span class="token class-name">value</span> <span class="token operator">=</span> <span class="token number">1500</span>
payload_bytes <span class="token operator">=</span> <span class="token keyword keyword-struct">struct</span><span class="token punctuation">.</span><span class="token function">pack</span><span class="token punctuation">(</span><span class="token char">'&lt;I'</span><span class="token punctuation">,</span> value<span class="token punctuation">)</span> <span class="token operator">+</span> b<span class="token char">'\x00\x00\x00\x00'</span>
payload <span class="token operator">=</span> <span class="token keyword keyword-struct">struct</span><span class="token punctuation">.</span><span class="token function">unpack</span><span class="token punctuation">(</span><span class="token char">'&lt;Q'</span><span class="token punctuation">,</span> payload_bytes<span class="token punctuation">)</span><span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span>
<span class="token macro property"><span class="token directive-hash">#</span> <span class="token expression">Result<span class="token operator">:</span> <span class="token number">0x00000000000005DC</span> <span class="token punctuation">(</span>identical<span class="token punctuation">)</span></span></span>
</code></pre><p><strong>Q: How do I ensure my integer and floating-point data is transmitted correctly across different systems?</strong><br>
A: Follow these guidelines for reliable cross-platform data transmission:</p>
<ol>
<li><strong>For Integers</strong>: Always use the helper functions and validate ranges:</li>
</ol>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Safe integer encoding (works on all platforms)</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token class-name">RS485DataFormat</span><span class="token double-colon punctuation">::</span><span class="token function">validateIntegerRange</span><span class="token punctuation">(</span>value<span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">,</span> <span class="token number">500</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint64_t">uint64_t</span> payload <span class="token operator">=</span> <span class="token class-name">RS485DataFormat</span><span class="token double-colon punctuation">::</span><span class="token function">encodeInteger</span><span class="token punctuation">(</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
    driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><ol start="2">
<li><strong>For Floating-Point</strong>: Use IEEE 754 standard with validation:</li>
</ol>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Safe float encoding (works on all platforms)</span>
<span class="token keyword keyword-float">float</span> weight <span class="token operator">=</span> <span class="token number">3.14159f</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token class-name">RS485DataFormat</span><span class="token double-colon punctuation">::</span><span class="token function">validateFloatRange</span><span class="token punctuation">(</span>weight<span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1000.0f</span><span class="token punctuation">,</span> <span class="token number">1000.0f</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint64_t">uint64_t</span> payload <span class="token operator">=</span> <span class="token class-name">RS485DataFormat</span><span class="token double-colon punctuation">::</span><span class="token function">encodeFloat</span><span class="token punctuation">(</span>weight<span class="token punctuation">)</span><span class="token punctuation">;</span>
    driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"W001"</span><span class="token punctuation">,</span> payload<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><ol start="3">
<li><strong>For Future Decimal Support</strong>: The 8-byte payload can accommodate:
<ul>
<li><strong>Single precision float</strong>: 32-bit IEEE 754 (±3.4 × 10^38, ~7 decimal digits)</li>
<li><strong>Double precision float</strong>: 64-bit IEEE 754 (±1.7 × 10^308, ~15 decimal digits)</li>
<li><strong>Fixed-point decimal</strong>: 32-bit scaled integer (user-defined precision)</li>
</ul>
</li>
</ol>
<p><strong>Q: What happens if I send data from a Windows system to a Linux system or vice versa?</strong><br>
A: The data format is designed to be completely interoperable:</p>
<ol>
<li><strong>Same Wire Format</strong>: Both systems use identical 12-byte payload structure</li>
<li><strong>Same Byte Order</strong>: Little-endian format is consistent across x86/x64 platforms</li>
<li><strong>Same Standards</strong>: IEEE 754 floating-point works identically on both platforms</li>
<li><strong>Validation</strong>: Helper functions ensure data integrity across platform boundaries</li>
</ol>
<p>Example of cross-platform communication:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Windows system sends:</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Threshold = 250mA</span>

<span class="token comment">// Linux system receives and processes identically:</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>responseData<span class="token punctuation">,</span> <span class="token number">200</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// Decodes to exactly 250mA regardless of platform</span>
</code></pre><h3 id="83-general-questions">8.3 General Questions </h3>
<p><strong>Q: What is the default slave address used if no S001 command has been executed?</strong><br>
A: The default slave address is 0x00 (broadcast address). However, it's recommended to always explicitly set the slave address using S001 before sending U-series commands.</p>
<p><strong>Q: Can I connect multiple slave devices when using the Master Broadcasting API?</strong><br>
A: No. Due to hardware limitations, only one slave device should be connected when using the Master Broadcasting API (S-series commands). Multiple slaves responding simultaneously would cause bus collisions.</p>
<p><strong>Q: What if I get no response during broadcasting operations?</strong><br>
A: If you're not getting responses during broadcasting:</p>
<ol>
<li>Ensure only one slave device is connected to the RS485 bus</li>
<li>Check cable integrity and connections</li>
<li>Verify power to the slave device</li>
<li>Try a lower baud rate temporarily to rule out timing issues</li>
<li>Check termination resistors on the RS485 bus</li>
<li>Use the <code>detectMultipleDevices()</code> function to check for multiple devices</li>
</ol>
<p><strong>Q: How do I handle communication errors?</strong><br>
A: The driver provides comprehensive error handling through error codes and error callbacks. You can register an error callback to be notified of errors asynchronously, or check the return value of each API call.</p>
<p><strong>Q: How do I distinguish between transient and permanent errors?</strong><br>
A: The driver categorizes errors into two types:</p>
<ul>
<li><strong>Transient errors</strong>: May resolve with retries (e.g., TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)</li>
<li><strong>Permanent errors</strong>: Require user intervention (e.g., INVALID_PARAMETER, DEVICE_NOT_FOUND)</li>
</ul>
<p>You can implement retry logic for transient errors:</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Example retry logic</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">isTransientError</span><span class="token punctuation">(</span>error<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> <span class="token number">3</span><span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Wait and retry</span>
        std<span class="token double-colon punctuation">::</span>this_thread<span class="token double-colon punctuation">::</span><span class="token function">sleep_for</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token function">milliseconds</span><span class="token punctuation">(</span><span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        status <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span>dataKey<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>status <span class="token operator">==</span> RS485Error<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token keyword keyword-break">break</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p>See section 4.0.1 for a complete implementation example.</p>
<p><strong>Q: Is the driver thread-safe?</strong><br>
A: Yes, the driver is designed to be thread-safe. You can call API functions from multiple threads without worrying about race conditions. The driver uses internal synchronization to prevent conflicts.</p>
<h3 id="82-configuration-and-addressing">8.2 Configuration and Addressing </h3>
<p><strong>Q: How do I configure multiple slave devices?</strong><br>
A: To configure multiple slaves, you need to:</p>
<ol>
<li>Set the slave address using S001 (e.g., <code>configureSystemSettings("S001", 1)</code>)</li>
<li>Configure that slave using U-series commands via <code>configureUserSettings</code></li>
<li>Change the slave address using S001 again (e.g., <code>configureSystemSettings("S001", 2)</code>)</li>
<li>Configure the next slave using U-series commands via <code>configureUserSettings</code></li>
<li>Repeat for each slave</li>
</ol>
<p><strong>Q: Why are there separate functions <code>configureSystemSettings</code> and <code>configureUserSettings</code> instead of a single function?</strong><br>
A: The separation provides clearer distinction between S-series commands (system settings via broadcasting) and U-series commands (user settings to specific slaves). This design makes the API more intuitive, prevents misuse, and better reflects the underlying protocol differences between these command types.</p>
<p><strong>Q: Do S001 and U001 commands need to be differentiated even if we only have one slave device?</strong><br>
A: Yes, they should still be differentiated for several reasons:</p>
<ol>
<li>S001 uses broadcasting (address 0x00) while U001 targets a specific slave address</li>
<li>S001 sets the addressing context for subsequent U-series commands</li>
<li>The separation maintains consistency with the ZES protocol design</li>
<li>It provides better code clarity and maintainability</li>
<li>It allows for future expansion if additional slave devices are added later</li>
</ol>
<p><strong>Q: Why does the API use JSON format for A001 and A004 commands?</strong><br>
A: JSON format is used for several industry-standard reasons:</p>
<ol>
<li><strong>Flexibility</strong>: JSON can represent complex nested data structures without requiring fixed schemas</li>
<li><strong>Self-describing</strong>: The format is human-readable and self-documenting</li>
<li><strong>Extensibility</strong>: New fields can be added without breaking backward compatibility</li>
<li><strong>Widespread Support</strong>: JSON parsing libraries are available in virtually all programming languages</li>
<li><strong>Industry Standard</strong>: Modern serial communication protocols increasingly use JSON for complex data</li>
<li><strong>Compact Format</strong>: For complex data, JSON can be more compact than fixed binary formats with many optional fields</li>
</ol>
<p><strong>Q: What is the purpose of the RequestOptions structure in the requestData API?</strong><br>
A: The RequestOptions structure follows industry-standard patterns for serial communication APIs by providing:</p>
<ol>
<li><strong>Parameterized Requests</strong>: Allows filtering and limiting data without creating numerous specialized functions</li>
<li><strong>Pagination Support</strong>: Enables retrieving large datasets in manageable chunks (offset + maxRecords)</li>
<li><strong>Time-Based Filtering</strong>: Particularly useful for event logs to retrieve events within specific time ranges</li>
<li><strong>Type Filtering</strong>: Allows requesting only specific types of events or data</li>
<li><strong>Format Control</strong>: Provides options for controlling response format (e.g., compact JSON)</li>
<li><strong>Optional Usage</strong>: The parameter is optional, maintaining backward compatibility and simplicity for basic requests</li>
<li><strong>Extensibility</strong>: The structure can be extended with new fields without breaking existing code</li>
</ol>
<h3 id="83-response-handling-and-performance">8.3 Response Handling and Performance </h3>
<p><strong>Q: When should I use callbacks versus polling for response handling?</strong><br>
A: Choose based on your application needs:</p>
<ul>
<li>
<p><strong>Use callbacks when</strong>:</p>
<ul>
<li>Building event-driven applications (e.g., GUI interfaces)</li>
<li>Handling real-time monitoring where responses need immediate processing</li>
<li>Working with asynchronous workflows where you don't want to block execution</li>
</ul>
</li>
<li>
<p><strong>Use polling when</strong>:</p>
<ul>
<li>Implementing sequential workflows where order matters</li>
<li>Writing simpler scripts without callback complexity</li>
<li>Working in environments where threading is limited</li>
</ul>
</li>
<li>
<p><strong>Use blocking calls when</strong>:</p>
<ul>
<li>Writing the simplest possible code</li>
<li>In scripts where blocking is acceptable</li>
<li>When you need guaranteed sequential processing</li>
</ul>
</li>
</ul>
<p>See section 4.7.1 for implementation examples of each approach.</p>
<p><strong>Q: How do I prevent buffer overflow during long operations?</strong><br>
A: You can:</p>
<ol>
<li>Set a buffer threshold and register a callback to be notified when the buffer usage exceeds that threshold</li>
<li>Configure the buffer overflow policy to determine how overflow situations are handled</li>
<li>Regularly call <code>receiveSlaveResponse</code> to process data from the buffer</li>
<li>Use a dedicated thread for handling responses</li>
</ol>
<p><strong>Q: What is the maximum transmission speed supported?</strong><br>
A: The driver supports baud rates up to 115200 bps. However, the actual throughput may be lower due to the protocol overhead and the half-duplex nature of RS485.</p>
<p><strong>Q: How can I optimize performance when working with multiple slave devices?</strong><br>
A: For optimal performance:</p>
<ol>
<li>Use non-blocking operations with callbacks to handle responses asynchronously</li>
<li>Implement a dedicated response handling thread</li>
<li>Batch commands where possible to reduce protocol overhead</li>
<li>Use appropriate buffer sizes based on expected data volumes</li>
<li>Consider using higher baud rates for shorter cable runs</li>
<li>Implement intelligent retry logic for transient errors</li>
</ol>
<h2 id="9-conclusion">9. Conclusion </h2>
<p>The AI-SLDAP RS485 Driver API provides a comprehensive solution for communicating with AI-SLDAP devices over RS485, implementing the ZES protocol with key enhancements for reliability and performance.</p>
<h3 id="91-api-summary">9.1 API Summary </h3>
<table>
<thead>
<tr>
<th>ZES Driver API Category</th>
<th>API Function</th>
<th>Purpose</th>
<th>Command Series</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Error Handle API</strong></td>
<td><code>getErrorString(error)</code></td>
<td>Provide detailed error information with transient vs. permanent categorization</td>
<td>N/A</td>
</tr>
<tr>
<td><strong>Master Broadcasting API</strong></td>
<td><code>configureSystemSettings(commandKey, value)</code></td>
<td>Configure system parameters via broadcasting</td>
<td>S-series</td>
</tr>
<tr>
<td><strong>Master Assign Data API</strong></td>
<td><code>configureUserSettings(commandKey, value)</code></td>
<td>Configure user parameters on slave devices</td>
<td>U-series</td>
</tr>
<tr>
<td><strong>Master Assign Data API</strong></td>
<td><code>modelDataOperation(address, data, isWrite, length)</code></td>
<td>Manage AI model data in FRAM memory</td>
<td>W-series</td>
</tr>
<tr>
<td><strong>Master Request API</strong></td>
<td><code>requestData(dataKey, options)</code></td>
<td>Request information from slaves with non-blocking operation</td>
<td>A-series</td>
</tr>
<tr>
<td><strong>Slave Response API</strong></td>
<td><code>receiveSlaveResponse(responseData, timeout)</code></td>
<td>Receive data with fixed-size payload buffer management (180 bytes total)</td>
<td>N/A</td>
</tr>
</tbody>
</table>
<h3 id="92-key-design-features">9.2 Key Design Features </h3>
<ul>
<li><strong>Cross-Platform API Consistency</strong>: Identical API interface on Windows and Linux platforms, following industry-standard patterns</li>
<li><strong>Universal Data Format</strong>: IEEE 754 and little-endian standards ensure compatibility across systems and programming languages</li>
<li><strong>Unified API Format</strong>: Each category follows a consistent pattern, reducing learning curve</li>
<li><strong>Protocol Abstraction</strong>: Low-level details are handled by the driver, simplifying application code</li>
<li><strong>Reliable Communication</strong>: Mandatory acknowledgment mechanism replaces timeout-based detection</li>
<li><strong>Clear Addressing Mechanism</strong>: Explicit documentation of how A-series and U-series commands use the slave address set by S001</li>
<li><strong>Non-Blocking Design for Airborne Environments</strong>:
<ul>
<li>Critical for systems that must process multiple tasks concurrently</li>
<li>Prevents slow serial communication from blocking application threads</li>
<li>Ensures responsiveness in time-critical airborne applications</li>
<li>Implements asynchronous operation with background thread processing</li>
</ul>
</li>
<li><strong>Enhanced Error Handling</strong>:
<ul>
<li>Categorized error codes (transient vs. permanent) for intelligent recovery</li>
<li>Callback mechanism for asynchronous error notification</li>
<li>Example retry logic for transient errors</li>
<li>Consistent error handling across Windows and Linux platforms</li>
</ul>
</li>
<li><strong>Flexible Response Handling</strong>:
<ul>
<li>Support for both callback-based (event-driven) and polling-based (sequential) approaches</li>
<li>Clear guidance on when to use each approach</li>
<li>Examples for all response handling patterns</li>
</ul>
</li>
<li><strong>Comprehensive Buffer Management</strong>:
<ul>
<li>Fixed-size payload buffers (5×12 bytes uplink, 10×12 bytes downlink) focused on valid data</li>
<li>FIFO buffer system with data ready notification</li>
<li>Advanced overflow protection with configurable policies</li>
<li>Buffer threshold callbacks for proactive management</li>
</ul>
</li>
<li><strong>Runtime Safety Checks</strong>: Detection of multiple devices during broadcasting operations</li>
<li><strong>Cross-Platform Data Validation</strong>: Helper functions ensure data integrity across different systems and languages</li>
<li><strong>Performance Optimization</strong>:
<ul>
<li>Non-blocking design keeps application threads responsive</li>
<li>FIFO buffer system prevents data loss during slow serial transmission</li>
<li>Optimized 12-byte payload management eliminates unnecessary overhead</li>
</ul>
</li>
</ul>
<h3 id="93-benefits">9.3 Benefits </h3>
<p>This design addresses the specific requirements outlined in the RS485 Communication Software Protocol document, particularly:</p>
<ul>
<li>The need for reliable broadcast communication with acknowledgment</li>
<li>Non-blocking operation for airborne environments with multiple concurrent tasks</li>
<li>Comprehensive buffer management (256KB/512KB) to handle the inherently slow nature of serial communication</li>
<li>Clear documentation of the U-series command addressing mechanism</li>
<li>Intelligent error handling with transient vs. permanent categorization and retry logic</li>
<li>Flexible response handling approaches with clear guidance on when to use each</li>
<li>Runtime safety checks to prevent common issues</li>
<li>FIFO buffer system with data ready notification to prevent data loss</li>
</ul>
<p>The result is a modular, extensible, and reliable communication system that can be easily integrated into various applications while maintaining performance in challenging airborne environments. The non-blocking design ensures that slow serial communication does not interfere with other critical operations, while the comprehensive buffer management prevents data loss during periods of high system load. The error handling and response flexibility make the API robust and user-friendly.</p>
<h2 id="10-critical-design-updates-summary">10. Critical Design Updates Summary </h2>
<h3 id="100-document-consistency-corrections-latest-update">10.0 Document Consistency Corrections (Latest Update) </h3>
<p><strong>Major Consistency Fixes Applied:</strong></p>
<ol>
<li>
<p><strong>Type Safety Enhancement</strong>: Replaced generic <code>RS485Error</code> with specific result types throughout the document:</p>
<ul>
<li><code>ConfigurationResult</code> for system and user configuration operations</li>
<li><code>RequestResult</code> for data request operations</li>
<li><code>ResponseResult</code> for response handling operations</li>
<li><code>ConnectionResult</code> for port and device connection operations</li>
<li><code>BufferResult</code> for buffer management operations</li>
</ul>
</li>
<li>
<p><strong>API Function Signature Consistency</strong>: Ensured all function signatures match their declared return types:</p>
<ul>
<li><code>configureSystemSettings()</code> returns <code>ConfigurationResult</code></li>
<li><code>configureUserSettings()</code> returns <code>ConfigurationResult</code></li>
<li><code>requestData()</code> returns <code>RequestResult</code></li>
<li><code>receiveSlaveResponse()</code> returns <code>ResponseResult</code></li>
<li><code>open()</code> returns <code>ConnectionResult</code></li>
<li><code>getBufferStatus()</code> returns <code>BufferResult</code></li>
</ul>
</li>
<li>
<p><strong>Error Handling Examples Updated</strong>: All code examples now use the correct specific result types instead of generic <code>RS485Error</code></p>
</li>
<li>
<p><strong>API Summary Table Corrections</strong>: Updated function signatures and descriptions to match the actual implementation</p>
</li>
<li>
<p><strong>Buffer Management Clarification</strong>: Consistently described the 12-byte payload-centric buffer design throughout the document</p>
</li>
</ol>
<h3 id="101-buffer-architecture-corrections">10.1 Buffer Architecture Corrections </h3>
<p><strong>Updated Buffer Specifications:</strong></p>
<ul>
<li><strong>Uplink Buffer</strong>: 5 payload slots × 12 bytes = 60 bytes total</li>
<li><strong>Downlink Buffer</strong>: 10 payload slots × 12 bytes = 120 bytes total</li>
<li><strong>Core Focus</strong>: Buffer management only focuses on <strong>12-byte payload</strong> data, which is the only valid information</li>
</ul>
<p><strong>Key Design Philosophy: Buffer Only Manages Valid Data</strong></p>
<p>Core design principles of the buffer management system:</p>
<ul>
<li><strong>Store only payload data</strong>: Buffer stores only the 12-byte valid payload data, not the frame's header, CRC, trailer, etc.</li>
<li><strong>Separate frame assembly/parsing</strong>: The 16-byte frame structure (0xAA + ID + 12-byte payload + CRC + 0x0D) is only assembled during transmission and parsed during reception</li>
<li><strong>Focus on valid data</strong>: Avoid storing redundant frame structure data, focus on valid information</li>
</ul>
<p><strong>Buffer Capacity Calculation:</strong></p>
<ul>
<li><strong>Uplink Buffer</strong>: 5 payload slots × 12 bytes = 60 bytes</li>
<li><strong>Downlink Buffer</strong>: 10 payload slots × 12 bytes = 120 bytes</li>
<li><strong>Total Capacity</strong>: 60 + 120 = 180 bytes (pure valid data)</li>
</ul>
<p><strong>Protocol Core</strong>: 12-byte payload contains all core communication data:</p>
<ul>
<li>System configuration commands (S-series): Configure system parameters</li>
<li>User configuration commands (U-series): User setting parameters</li>
<li>Application data requests (A-series): Application data queries</li>
<li>AI model weight/bias data (W-series): AI model weight data</li>
</ul>
<p><strong>Design Advantages:</strong></p>
<ul>
<li>Simplified buffer management logic, focused on valid data processing</li>
<li>Reduced unnecessary memory copying</li>
</ul>
<h3 id="102-buffer-flag-management-implementation">10.2 Buffer Flag Management Implementation </h3>
<p><strong>Critical Buffer Overflow Prevention:</strong></p>
<ul>
<li><strong>Pre-transmission Check</strong>: Driver checks uplink buffer flag before sending data to ensure space is available</li>
<li><strong>Pre-storage Check</strong>: Driver checks downlink buffer flag before storing received data to prevent overflow</li>
<li><strong>FIFO Guarantee</strong>: Strict First-In-First-Out ordering maintained for both PC User side and driver side</li>
<li><strong>Overflow Handling</strong>: Configurable policies when buffers reach capacity (discard oldest/newest or trigger error)</li>
</ul>
<p><strong>Buffer Flag Structure:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-struct">struct</span> <span class="token class-name">BufferFlags</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-bool">bool</span> uplinkFull<span class="token punctuation">;</span>        <span class="token comment">// Uplink buffer full flag (when 5 payload slots used)</span>
    <span class="token keyword keyword-bool">bool</span> downlinkFull<span class="token punctuation">;</span>      <span class="token comment">// Downlink buffer full flag (when 10 payload slots used)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkUsed<span class="token punctuation">;</span>    <span class="token comment">// Current uplink usage (0-5 payload slots)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkUsed<span class="token punctuation">;</span>  <span class="token comment">// Current downlink usage (0-10 payload slots)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="103-deviceiocontrol-implementation-strategy">10.3 DeviceIoControl() Implementation Strategy </h3>
<p><strong>API Design Philosophy:</strong></p>
<ul>
<li><strong>Internal Implementation</strong>: DeviceIoControl() is used <strong>internally within the API functions</strong>, not exposed directly to users</li>
<li><strong>User-Friendly Interface</strong>: Applications use high-level API functions (configureSystemSettings, requestData, etc.)</li>
<li><strong>Industry Standard</strong>: Follows standard serial port communication interface patterns</li>
<li><strong>Abstraction Layer</strong>: Users don't need to understand IOCTL codes or buffer management details</li>
</ul>
<p><strong>Data Exchange Mechanism:</strong></p>
<ol>
<li><strong>Standard Windows Interface</strong>: Uses the industry-standard Windows driver communication method</li>
<li><strong>Asynchronous I/O Support</strong>: Enables non-blocking operations with overlapped I/O</li>
<li><strong>Buffer Management</strong>: Efficient data transfer through system-managed buffers</li>
<li><strong>Error Handling</strong>: Comprehensive error reporting through Windows error codes</li>
<li><strong>FIFO Guarantee</strong>: DeviceIoControl() calls are queued and processed in strict FIFO order</li>
</ol>
<h3 id="104-payload-centric-data-flow">10.4 Payload-Centric Data Flow </h3>
<p><strong>Complete Data Flow Architecture:</strong></p>
<ol>
<li><strong>Transmission Path</strong>: PC User → Buffer Flag Check → 12-byte Payload Queue → Frame Assembly → RS485 Bus</li>
<li><strong>Reception Path</strong>: RS485 Bus → Frame Parsing → 12-byte Payload Extraction → Buffer Flag Check → Payload Queue → PC User</li>
<li><strong>Core Principle</strong>: All meaningful data exchange occurs through the 12-byte payload, making it the heart of the protocol</li>
</ol>
<p><strong>Frame Processing Pipeline:</strong></p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>FTDI VCP Driver → Filter Driver → Frame Parser → Payload Extractor → Buffer Flag Check → Buffer Manager → User Application
     ↓              ↓              ↓              ↓                ↓                ↓              ↓
  Hardware IRQ → Work Item → DPC Context → 12-byte payload → Check downlink flag → Ring Buffer → IOCTL Completion
</code></pre><h3 id="105-enhanced-driver-architecture">10.5 Enhanced Driver Architecture </h3>
<h1 id="ai-sldap-rs485-driver-api-design-document-1">AI-SLDAP RS485 Driver API Design Document </h1>
<p><strong>Buffer Flag Manager:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">BufferFlagManager</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Pre-transmission buffer flag check</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">CheckUplinkSpaceAvailable</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Pre-storage buffer flag check</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">CheckDownlinkSpaceAvailable</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Update flags after buffer operations</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">UpdateBufferFlags</span><span class="token punctuation">(</span>size_t uplinkUsed<span class="token punctuation">,</span> size_t downlinkUsed<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h3 id="106-implementation-summary">10.6 Implementation Summary </h3>
<p>These critical updates ensure that:</p>
<ol>
<li><strong>Buffer management is accurate</strong> with correct 12-byte payload focus</li>
<li><strong>Overflow prevention is robust</strong> with comprehensive flag checking</li>
<li><strong>FIFO ordering is guaranteed</strong> for both PC and driver sides</li>
<li><strong>DeviceIoControl() usage is properly abstracted</strong> within the API</li>
<li><strong>The 12-byte payload remains central</strong> to all protocol operations</li>
</ol>
<p>The updated design maintains the original API simplicity while providing the robust buffer management and overflow prevention mechanisms essential for reliable RS485 communication in airborne environments.</p>
<h2 id="11-function-code-to-api-category-mapping-summary">11. Function Code to API Category Mapping Summary </h2>
<h3 id="111-complete-function-code-correspondence">11.1 Complete Function Code Correspondence </h3>
<p>The RS485 driver implements a <strong>direct one-to-one mapping</strong> between ZES protocol function codes and API categories, ensuring that each API call is automatically routed to the correct protocol handling mechanism:</p>
<table>
<thead>
<tr>
<th style="text-align:center">Function Code</th>
<th style="text-align:center">Binary</th>
<th style="text-align:left">Description</th>
<th style="text-align:left">API Category</th>
<th style="text-align:left">Buffer Check</th>
<th style="text-align:left">Example Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center"><strong>0b111</strong></td>
<td style="text-align:center">Assign data</td>
<td style="text-align:left"><strong>Master Broadcasting API</strong> (S-series)</td>
<td style="text-align:left">✓ Uplink</td>
<td style="text-align:left"><code>configureSystemSettings("S001", 5)</code></td>
<td style="text-align:left"></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b111</strong></td>
<td style="text-align:center">Assign data</td>
<td style="text-align:left"><strong>Master Assign Data API</strong> (U/W-series)</td>
<td style="text-align:left">✓ Uplink</td>
<td style="text-align:left"><code>configureUserSettings("U001", 250)</code></td>
<td style="text-align:left"></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b110</strong></td>
<td style="text-align:center">Request data</td>
<td style="text-align:left"><strong>Master Request API</strong> (A-series)</td>
<td style="text-align:left">✓ Uplink</td>
<td style="text-align:left"><code>requestData("A001")</code></td>
<td style="text-align:left"></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b010</strong></td>
<td style="text-align:center">Response to Assign</td>
<td style="text-align:left"><strong>Slave Response API</strong> (Acknowledgments)</td>
<td style="text-align:left">✓ Downlink</td>
<td style="text-align:left"><code>receiveSlaveResponse(responseData, 1000)</code></td>
<td style="text-align:left"></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b001</strong></td>
<td style="text-align:center">Response to Request</td>
<td style="text-align:left"><strong>Slave Response API</strong> (Data responses)</td>
<td style="text-align:left">✓ Downlink</td>
<td style="text-align:left"><code>receiveSlaveResponse(responseData, 1000)</code></td>
<td style="text-align:left"></td>
</tr>
<tr>
<td style="text-align:center"><strong>0b000</strong></td>
<td style="text-align:center">Re-send request</td>
<td style="text-align:left"><strong>Error Handle API</strong> (Retry mechanism)</td>
<td style="text-align:left">N/A</td>
<td style="text-align:left">Automatic retry handling</td>
<td style="text-align:left"></td>
</tr>
</tbody>
</table>
<h3 id="112-ftdi-style-management-integration">11.2 FTDI-Style Management Integration </h3>
<p>The driver provides comprehensive management functions following industry-standard patterns:</p>
<p><strong>Port Management (Similar to FTDI FT_Open, FT_Close, FT_ListDevices):</strong></p>
<ul>
<li><code>openPort()</code>, <code>closePort()</code>, <code>isPortOpen()</code>, <code>enumerateDevices()</code></li>
<li><code>detectMultipleDevices()</code>, <code>getPortInfo()</code>, <code>getBaudRate()</code></li>
</ul>
<p><strong>Buffer Management (Critical for RS485 Communication):</strong></p>
<ul>
<li><code>getBufferStatus()</code>, <code>checkUplinkBufferFlag()</code>, <code>checkDownlinkBufferFlag()</code></li>
<li><code>clearBuffer()</code>, <code>setBufferOverflowPolicy()</code>, <code>getBufferCapacity()</code></li>
<li><code>setBufferThreshold()</code>, <code>registerBufferThresholdCallback()</code></li>
</ul>
<p><strong>Hardware Status (Similar to FTDI FT_GetStatus):</strong></p>
<ul>
<li><code>getHardwareStatus()</code>, <code>getPerformanceMetrics()</code>, <code>getLineStatus()</code></li>
</ul>
<h3 id="113-automatic-buffer-flag-checking">11.3 Automatic Buffer Flag Checking </h3>
<p><strong>Every data transmission operation includes mandatory buffer verification:</strong></p>
<ol>
<li><strong>Before Transmission</strong>: Uplink buffer flag checked to ensure space availability</li>
<li><strong>Before Storage</strong>: Downlink buffer flag checked to prevent overflow</li>
<li><strong>FIFO Guarantee</strong>: Strict First-In-First-Out ordering maintained</li>
<li><strong>Overflow Policies</strong>: Configurable handling (DISCARD_OLDEST, DISCARD_NEWEST, TRIGGER_ERROR)</li>
</ol>
<p><strong>Implementation Example:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Automatic buffer checking in every API call</span>
ConfigurationResult <span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> commandKey<span class="token punctuation">,</span> <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Step 1: Mandatory buffer flag check</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token function">checkUplinkBufferFlag</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>BUFFER_OVERFLOW<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 2: Function code validation (0b111 for S-series)</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token function">isValidSystemCommand</span><span class="token punctuation">(</span>commandKey<span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> ConfigurationResult<span class="token double-colon punctuation">::</span>INVALID_PARAMETER<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Step 3: Process with automatic function code routing</span>
    <span class="token keyword keyword-return">return</span> <span class="token function">processAssignDataCommand</span><span class="token punctuation">(</span><span class="token number">0b111</span><span class="token punctuation">,</span> <span class="token number">0x00</span><span class="token punctuation">,</span> commandKey<span class="token punctuation">,</span> value<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="114-enhanced-error-management">11.4 Enhanced Error Management </h3>
<p><strong>Comprehensive error handling with FTDI integration:</strong></p>
<ul>
<li><strong>FTDI Driver Errors (100-199)</strong>: Direct mapping from FTDI VCP driver errors</li>
<li><strong>Buffer Management Errors (150-199)</strong>: Critical for RS485 operation</li>
<li><strong>Protocol Errors (200-299)</strong>: ZES-specific error handling with automatic retry</li>
<li><strong>Function Code Errors (500-599)</strong>: Ensures API calls match protocol requirements</li>
</ul>
<p><strong>Error categorization enables intelligent recovery:</strong></p>
<ul>
<li><strong>Transient errors</strong>: May succeed on retry (TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)</li>
<li><strong>Permanent errors</strong>: Require user intervention (INVALID_PARAMETER, DEVICE_NOT_FOUND)</li>
</ul>
<p>This comprehensive design ensures that the five API categories directly correspond to the ZES protocol function codes, providing a reliable and industry-standard interface for RS485 communication while maintaining automatic buffer management and error handling.</p>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>