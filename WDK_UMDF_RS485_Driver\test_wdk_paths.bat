@echo off
REM 测试WDK路径配置脚本

echo ========================================
echo 测试WDK路径配置
echo ========================================

echo 检查WDF头文件路径...
if exist "C:\Program Files (x86)\Windows Kits\10\Include\wdf\umdf\2.0\wdf.h" (
    echo ✓ WDF头文件路径正确: wdf.h 找到
) else (
    echo ✗ WDF头文件路径错误: wdf.h 未找到
)

echo.
echo 检查WDF库文件路径...
if exist "C:\Program Files (x86)\Windows Kits\10\Lib\wdf\umdf\x64\2.0\WdfDriverStubUm.lib" (
    echo ✓ WDF库文件路径正确: WdfDriverStubUm.lib 找到
) else (
    echo ✗ WDF库文件路径错误: WdfDriverStubUm.lib 未找到
)

echo.
echo 检查Windows SDK路径...
if exist "C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\windows.h" (
    echo ✓ Windows SDK路径正确: windows.h 找到
) else (
    echo ✗ Windows SDK路径错误: windows.h 未找到
)

echo.
echo 检查Visual Studio MSBuild...
set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022
set MSBUILD_PATH=%VS_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe

if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe
)

if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe
)

if exist "%MSBUILD_PATH%" (
    echo ✓ Visual Studio MSBuild找到: %MSBUILD_PATH%
) else (
    echo ✗ Visual Studio MSBuild未找到
)

echo.
echo ========================================
echo 路径检查完成
echo ========================================

echo 如果所有路径都显示 ✓，那么现在可以尝试编译了
echo 请使用以下方法之一：
echo 1. 双击 build_vs2022.bat
echo 2. 在Visual Studio中打开解决方案并编译
echo.
echo 按任意键退出...
pause >nul
