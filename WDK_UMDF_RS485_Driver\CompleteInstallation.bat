@echo off
echo ===================================================================
echo RS485 UMDF Driver - Complete Installation Solution
echo Secure Boot Compatible - No Test Signing Required
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo This script will:
echo 1. Create a self-signed certificate for driver signing
echo 2. Build and sign the UMDF driver
echo 3. Create a signed catalog file
echo 4. Build the installer with signed components
echo 5. Install the complete RS485 driver solution
echo.
echo This works with Secure Boot enabled and does not require test signing.
echo.

set /p CONTINUE=Continue with installation? (Y/N): 
if /i not "%CONTINUE%"=="Y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo ===================================================================
echo Step 1: Creating driver certificate...
echo ===================================================================
call CreateDriverCertificate.bat

echo.
echo ===================================================================
echo Step 2: Building and signing driver...
echo ===================================================================
call BuildSignedDriver.bat

echo.
echo ===================================================================
echo Step 3: Installing the driver...
echo ===================================================================
echo.
echo Running the signed installer...
.\FinalOutput\RS485DriverInstaller.exe

echo.
echo ===================================================================
echo Installation Process Complete!
echo ===================================================================
echo.
echo Your RS485 UMDF driver has been installed with proper signing.
echo This installation is compatible with Secure Boot and enterprise policies.
echo.
echo To verify installation:
echo 1. Connect your FTDI device
echo 2. Check Device Manager under "Ports (COM & LPT)"
echo 3. Verify the RS485 filter driver is loaded
echo.

pause
