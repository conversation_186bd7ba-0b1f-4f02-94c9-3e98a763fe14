@echo off
REM ===================================================================
REM Quick Test for Error 0xE000022F Fix
REM ===================================================================

echo ===================================================================
echo RS485 Driver Installer - Error 0xE000022F Fix Test
echo ===================================================================
echo.
echo This test verifies the enhanced error handling for catalog file issues.
echo.

echo Current system status:
echo ===================================================================

echo Test signing status:
bcdedit /enum {current} | findstr /i testsigning
if %errorLevel% neq 0 (
    echo Test signing: DISABLED
    echo Note: Enable with 'bcdedit /set testsigning on' if needed
) else (
    echo Test signing: ENABLED
)
echo.

echo Administrator privileges:
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Status: NOT running as administrator
    echo Note: Some features may be limited
) else (
    echo Status: Running as administrator ✓
)
echo.

echo ===================================================================
echo Expected Behavior:
echo ===================================================================
echo.
echo 1. If error 0xE000022F occurs, installer will:
echo    - Detect catalog file missing/invalid
echo    - Provide specific error guidance
echo    - Automatically try alternative installation method
echo    - Use simplified INF without catalog requirements
echo.
echo 2. Alternative installation will:
echo    - Copy DLL to System32 directory
echo    - Create simplified INF for development
echo    - Complete installation successfully
echo.

echo Ready to test? Press any key to run installer...
pause >nul

echo.
echo Running enhanced installer...
echo ===================================================================
.\FinalOutput\RS485DriverInstaller.exe

echo.
echo ===================================================================
echo Test Results Analysis:
echo ===================================================================
echo.
echo If you saw:
echo ✓ "ERROR: Catalog file missing or security validation failed"
echo ✓ "Trying alternative installation method..."
echo ✓ "Alternative installation completed"
echo.
echo Then the fix is working correctly! ✅
echo.
echo If you still see "Unknown error code", please check:
echo - Ensure you're using the latest built installer
echo - Verify all code changes were compiled
echo.

pause
