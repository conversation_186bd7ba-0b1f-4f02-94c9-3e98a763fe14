﻿  DriverEntry.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“DriverEntry.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  DllSupport.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“DllSupport.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485Buffer.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Buffer.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485Device.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Device.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485FilterDriver.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485FilterDriver.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485Protocol.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Protocol.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485Queue.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485Queue.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485ErrorHandling.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485ErrorHandling.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485ProtocolHandlers.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485ProtocolHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  RS485IOCTLHandlers.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: “STATUS_WAIT_0”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      参见“STATUS_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: “STATUS_ABANDONED_WAIT_0”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      参见“STATUS_ABANDONED_WAIT_0”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: “STATUS_USER_APC”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      参见“STATUS_USER_APC”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      参见“STATUS_TIMEOUT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      参见“STATUS_PENDING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: “DBG_EXCEPTION_HANDLED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      参见“DBG_EXCEPTION_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: “DBG_CONTINUE”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      参见“DBG_CONTINUE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: “STATUS_SEGMENT_NOTIFICATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      参见“STATUS_SEGMENT_NOTIFICATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: “STATUS_FATAL_APP_EXIT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      参见“STATUS_FATAL_APP_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: “DBG_REPLY_LATER”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      参见“DBG_REPLY_LATER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: “DBG_TERMINATE_THREAD”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      参见“DBG_TERMINATE_THREAD”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: “DBG_TERMINATE_PROCESS”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      参见“DBG_TERMINATE_PROCESS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: “DBG_CONTROL_C”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      参见“DBG_CONTROL_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: “DBG_PRINTEXCEPTION_C”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      参见“DBG_PRINTEXCEPTION_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: “DBG_RIPEXCEPTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      参见“DBG_RIPEXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: “DBG_CONTROL_BREAK”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      参见“DBG_CONTROL_BREAK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: “DBG_COMMAND_EXCEPTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      参见“DBG_COMMAND_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: “DBG_PRINTEXCEPTION_WIDE_C”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      参见“DBG_PRINTEXCEPTION_WIDE_C”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: “STATUS_GUARD_PAGE_VIOLATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      参见“STATUS_GUARD_PAGE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: “STATUS_DATATYPE_MISALIGNMENT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      参见“STATUS_DATATYPE_MISALIGNMENT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: “STATUS_BREAKPOINT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      参见“STATUS_BREAKPOINT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: “STATUS_SINGLE_STEP”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      参见“STATUS_SINGLE_STEP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: “STATUS_LONGJUMP”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      参见“STATUS_LONGJUMP”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: “STATUS_UNWIND_CONSOLIDATE”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      参见“STATUS_UNWIND_CONSOLIDATE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: “DBG_EXCEPTION_NOT_HANDLED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      参见“DBG_EXCEPTION_NOT_HANDLED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: “STATUS_ACCESS_VIOLATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      参见“STATUS_ACCESS_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: “STATUS_IN_PAGE_ERROR”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      参见“STATUS_IN_PAGE_ERROR”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: “STATUS_INVALID_HANDLE”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      参见“STATUS_INVALID_HANDLE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: “STATUS_INVALID_PARAMETER”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      参见“STATUS_INVALID_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: “STATUS_NO_MEMORY”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      参见“STATUS_NO_MEMORY”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: “STATUS_ILLEGAL_INSTRUCTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      参见“STATUS_ILLEGAL_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: “STATUS_NONCONTINUABLE_EXCEPTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      参见“STATUS_NONCONTINUABLE_EXCEPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: “STATUS_INVALID_DISPOSITION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      参见“STATUS_INVALID_DISPOSITION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: “STATUS_ARRAY_BOUNDS_EXCEEDED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      参见“STATUS_ARRAY_BOUNDS_EXCEEDED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: “STATUS_FLOAT_DENORMAL_OPERAND”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      参见“STATUS_FLOAT_DENORMAL_OPERAND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: “STATUS_FLOAT_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      参见“STATUS_FLOAT_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: “STATUS_FLOAT_INEXACT_RESULT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      参见“STATUS_FLOAT_INEXACT_RESULT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: “STATUS_FLOAT_INVALID_OPERATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      参见“STATUS_FLOAT_INVALID_OPERATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: “STATUS_FLOAT_OVERFLOW”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      参见“STATUS_FLOAT_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: “STATUS_FLOAT_STACK_CHECK”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      参见“STATUS_FLOAT_STACK_CHECK”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: “STATUS_FLOAT_UNDERFLOW”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      参见“STATUS_FLOAT_UNDERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: “STATUS_INTEGER_DIVIDE_BY_ZERO”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      参见“STATUS_INTEGER_DIVIDE_BY_ZERO”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: “STATUS_INTEGER_OVERFLOW”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      参见“STATUS_INTEGER_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: “STATUS_PRIVILEGED_INSTRUCTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      参见“STATUS_PRIVILEGED_INSTRUCTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: “STATUS_STACK_OVERFLOW”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      参见“STATUS_STACK_OVERFLOW”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: “STATUS_DLL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      参见“STATUS_DLL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: “STATUS_ORDINAL_NOT_FOUND”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      参见“STATUS_ORDINAL_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: “STATUS_ENTRYPOINT_NOT_FOUND”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      参见“STATUS_ENTRYPOINT_NOT_FOUND”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: “STATUS_CONTROL_C_EXIT”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      参见“STATUS_CONTROL_C_EXIT”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: “STATUS_DLL_INIT_FAILED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      参见“STATUS_DLL_INIT_FAILED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: “STATUS_CONTROL_STACK_VIOLATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      参见“STATUS_CONTROL_STACK_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: “STATUS_FLOAT_MULTIPLE_FAULTS”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      参见“STATUS_FLOAT_MULTIPLE_FAULTS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: “STATUS_FLOAT_MULTIPLE_TRAPS”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      参见“STATUS_FLOAT_MULTIPLE_TRAPS”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: “STATUS_REG_NAT_CONSUMPTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      参见“STATUS_REG_NAT_CONSUMPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: “STATUS_HEAP_CORRUPTION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      参见“STATUS_HEAP_CORRUPTION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: “STATUS_STACK_BUFFER_OVERRUN”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      参见“STATUS_STACK_BUFFER_OVERRUN”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: “STATUS_INVALID_CRUNTIME_PARAMETER”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      参见“STATUS_INVALID_CRUNTIME_PARAMETER”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: “STATUS_ASSERTION_FAILURE”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      参见“STATUS_ASSERTION_FAILURE”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: “STATUS_ENCLAVE_VIOLATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      参见“STATUS_ENCLAVE_VIOLATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: “STATUS_INTERRUPTED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      参见“STATUS_INTERRUPTED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: “STATUS_THREAD_NOT_RUNNING”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      参见“STATUS_THREAD_NOT_RUNNING”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: “STATUS_ALREADY_REGISTERED”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      参见“STATUS_ALREADY_REGISTERED”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: “STATUS_SXS_EARLY_DEACTIVATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      参见“STATUS_SXS_EARLY_DEACTIVATION”的前一个定义
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15317,9): warning C4005: “STATUS_SXS_INVALID_DEACTIVATION”: 宏重定义
  (编译源文件“RS485IOCTLHandlers.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      参见“STATUS_SXS_INVALID_DEACTIVATION”的前一个定义
  
  正在生成代码...
    正在创建库 D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Build\Debug\x64\RS485FilterDriver.lib 和对象 D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Build\Debug\x64\RS485FilterDriver.exp
  RS485FilterDriver.vcxproj -> D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Build\Debug\x64\RS485FilterDriver.dll
          1 file(s) copied.
