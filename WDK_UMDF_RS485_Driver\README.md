# RS485 Driver Development Project - WDK UMDF Implementation

## Project Overview

This project implements a Windows User-Mode Driver Framework (UMDF) based RS485 communication driver for AI-SLDAP devices. The driver provides a complete solution for RS485 communication using the ZES protocol with integrated FTDI VCP functionality.

## Architecture

- **Driver Type**: UMDF 2.0 Filter Driver (sits above FTDI VCP driver)
- **Protocol**: ZES proprietary protocol with 16-byte frames
- **Payload Management**: 12-byte payload buffers (5 uplink × 12 bytes + 10 downlink × 12 bytes)
- **Communication**: Asynchronous request-response pattern with buffer overflow protection

## Project Structure

```
WDK_UMDF_RS485_Driver/
├── Driver/                     # UMDF driver implementation
│   ├── RS485FilterDriver.cpp   # Main driver entry point
│   ├── RS485Device.cpp         # Device object implementation
│   ├── RS485Queue.cpp          # I/O queue management
│   ├── RS485Buffer.cpp         # Buffer management (12-byte payload focus)
│   ├── RS485Protocol.cpp       # ZES protocol processing
│   ├── RS485FilterDriver.h     # Driver header definitions
│   ├── RS485FilterDriver.def   # Driver export definitions
│   ├── RS485FilterDriver.inf   # Driver installation information
│   └── sources                 # WDK build configuration
├── Interface/                  # Application interface library
│   ├── RS485DriverInterface.cpp # High-level API implementation
│   ├── RS485DriverInterface.h   # Public API header
│   └── sources                 # Interface library build config
├── Test/                       # Test applications
│   ├── RS485Test.cpp           # Basic functionality test
│   ├── RS485ProtocolTest.cpp   # Protocol compliance test
│   └── sources                 # Test application build config
├── Include/                    # Shared header files
│   ├── RS485Common.h           # Common definitions
│   ├── RS485Protocol.h         # Protocol structures
│   └── RS485Errors.h           # Error code definitions
└── Build/                      # Build output directory
    ├── Debug/                  # Debug build outputs
    └── Release/                # Release build outputs
```

## Development Environment Requirements

1. **Visual Studio 2022** with Desktop development with C++ workload
2. **Windows Driver Kit (WDK)** - Latest version for Windows 10/11
3. **Required Components**:
   - MSVC v143 - VS 2022 C++ x64/x86 Spectre-mitigated libs
   - C++ ATL for latest v143 build tools with Spectre Mitigations
   - Windows Driver Kit extension for Visual Studio

## Key Features

### Driver Features
- **UMDF 2.0 Filter Driver**: Integrates with existing FTDI VCP driver
- **Payload-Centric Design**: Focuses on 12-byte payload data handling
- **Buffer Management**: Fixed-size buffers with overflow protection
- **Asynchronous Processing**: Non-blocking I/O with work items and DPCs
- **Function Code Routing**: Automatic routing based on ZES protocol function codes

### API Features
- **High-Level Interface**: Abstracts DeviceIoControl complexity
- **Cross-Platform Data Format**: Universal data encoding for different systems
- **Automatic Type Conversion**: Users provide raw values, driver handles conversion
- **Buffer Flag Checking**: Mandatory checks before transmission/storage
- **Error Categorization**: Specific result types for different operation categories

## Protocol Implementation

### ZES Protocol Frame Structure (16 bytes)
```
[Header(1)] + [ID(1)] + [Payload(12)] + [CRC(1)] + [Trailer(1)]
   0xAA    +  ID Byte + Key(4)+Value(8) +  CRC8  +    0x0D
```

### Function Code Mapping
- **0b111**: Assign data (S-series, U-series, W-series commands)
- **0b110**: Request data (A-series commands)
- **0b010**: Response to Assign (acknowledgments)
- **0b001**: Response to Request (data responses)
- **0b000**: Re-send request (error recovery)

## Build Instructions

1. Open Visual Studio 2022 with WDK installed
2. Load the driver solution file
3. Select target architecture (x64 recommended)
4. Build configuration (Debug/Release)
5. Deploy driver for testing (requires test signing)

## Testing and Deployment

### Development Testing
- Use Visual Studio's driver deployment features
- Enable test signing on development machines
- Use Device Manager for driver installation verification

### Production Deployment
- Code signing certificate required for distribution
- INF-based installation through Device Manager
- Windows Update integration possible

## Documentation

- Complete API reference in design document
- Protocol specification with examples
- Error handling guidelines
- Cross-platform compatibility notes

## Notes

This implementation follows the complete design specification in `RS485_Driver_API_Design_Document_Updated.md` with focus on:
- Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2)
- 12-byte payload buffer management
- ZES protocol compliance
- FTDI VCP driver integration
- Comprehensive error handling
