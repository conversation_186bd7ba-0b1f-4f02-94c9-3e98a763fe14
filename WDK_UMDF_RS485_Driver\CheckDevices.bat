@echo off
echo ===================================================================
echo RS485 Device Detection Tool
echo ===================================================================
echo.

echo Checking for FTDI devices in Device Manager...
echo.

REM Use PowerShell to query device information
powershell -Command "Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.DeviceID -like '*VID_0403*' -or $_.Name -like '*FTDI*' -or $_.Name -like '*USB Serial*'} | Select-Object Name, DeviceID, Status | Format-Table -AutoSize"

echo.
echo Checking COM ports...
echo.

REM List COM ports
powershell -Command "Get-WmiObject -Class Win32_SerialPort | Select-Object DeviceID, Description, Status | Format-Table -AutoSize"

echo.
echo Checking RS485 driver files...
echo.

if exist "C:\Windows\System32\RS485FilterDriver.dll" (
    echo ✅ RS485FilterDriver.dll found in System32
) else (
    echo ❌ RS485FilterDriver.dll NOT found in System32
)

if exist "Driver\Build\Release\x64\RS485FilterDriver.dll" (
    echo ✅ RS485FilterDriver.dll found in build directory
) else (
    echo ❌ RS485FilterDriver.dll NOT found in build directory
)

echo.
echo ===================================================================
echo Device Detection Complete
echo ===================================================================
echo.
echo If you see FTDI devices listed above, your hardware is detected.
echo If you see COM ports listed, you can test communication.
echo.

pause
