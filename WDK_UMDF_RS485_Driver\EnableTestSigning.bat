@echo off
echo ===================================================================
echo Enable Test Signing for RS485 Driver
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Enabling test signing mode...
bcdedit /set testsigning on

if %errorLevel% equ 0 (
    echo.
    echo ===================================================================
    echo SUCCESS: Test signing has been enabled!
    echo ===================================================================
    echo.
    echo IMPORTANT: You must REBOOT your computer for this change to take effect.
    echo.
    echo After reboot:
    echo 1. Run: .\FinalOutput\RS485DriverInstaller.exe
    echo 2. The driver should install without the catalog file error
    echo.
    echo Would you like to reboot now? (Y/N)
    set /p REBOOT=
    if /i "%REBOOT%"=="Y" (
        echo Rebooting in 10 seconds...
        shutdown /r /t 10
    ) else (
        echo Please reboot manually when ready.
    )
) else (
    echo.
    echo ERROR: Failed to enable test signing.
    echo This may happen if:
    echo 1. Secure Boot is enabled (disable in BIOS)
    echo 2. BitLocker is active
    echo 3. System policy prevents changes
    echo.
    echo Please check your system configuration.
)

echo.
pause
