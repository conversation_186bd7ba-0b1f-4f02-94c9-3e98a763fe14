@echo off
echo ===================================================================
echo Building RS485 Test UI Application
echo ===================================================================
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1

echo Compiling RS485TestUI.cpp...
echo.

REM Compile the UI application
cl.exe ^
    /EHsc ^
    /std:c++17 ^
    /Fe:RS485TestUI.exe ^
    /DWIN32 ^
    /D_WINDOWS ^
    /DUNICODE ^
    /D_UNICODE ^
    RS485TestUI.cpp ^
    /link ^
    user32.lib ^
    gdi32.lib ^
    comctl32.lib ^
    /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo ===================================================================
    echo ✅ RS485 Test UI compiled successfully!
    echo ===================================================================
    echo.
    echo You can now run: .\RS485TestUI.exe
    echo.
    echo Features:
    echo - Graphical interface for RS485 testing
    echo - COM port selection and connection
    echo - Send/receive hex data
    echo - Built-in S001 and A001 command testing
    echo - Real-time data display with timestamps
    echo.
) else (
    echo.
    echo ❌ Compilation failed!
    echo.
    echo Trying alternative method...
    
    REM Try direct compilation
    "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" ^
        /EHsc /std:c++17 /Fe:RS485TestUI.exe /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE ^
        RS485TestUI.cpp /link user32.lib gdi32.lib comctl32.lib /SUBSYSTEM:WINDOWS
    
    if %errorLevel% equ 0 (
        echo ✅ Compilation successful with alternative method!
    ) else (
        echo ❌ Both compilation methods failed.
        echo Please ensure Visual Studio 2022 is properly installed.
    )
)

echo.
pause
