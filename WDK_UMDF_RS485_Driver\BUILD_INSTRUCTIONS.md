# RS485 Driver Build Instructions

## Prerequisites

### Required Software
1. **Visual Studio 2022** (Community, Professional, or Enterprise)
   - Desktop development with C++ workload
   - MSVC v143 - VS 2022 C++ x64/x86 Spectre-mitigated libs
   - C++ ATL for latest v143 build tools with Spectre Mitigations

2. **Windows Driver Kit (WDK)**
   - Latest version for Windows 10/11
   - Windows Driver Kit extension for Visual Studio
   - Download from: https://docs.microsoft.com/en-us/windows-hardware/drivers/download-the-wdk

3. **Windows SDK**
   - Windows 10/11 SDK (latest version)
   - Included with Visual Studio or downloadable separately

### System Requirements
- Windows 10 version 1903 or later
- Windows 11 (recommended)
- Administrator privileges for driver installation and testing

## Build Process

### Method 1: Visual Studio Build (Recommended)

1. **Open Solution**
   ```
   Open RS485Driver.sln in Visual Studio 2022
   ```

2. **Select Configuration**
   - Debug or Release
   - x64 (recommended) or x86

3. **Build Order**
   - Build RS485DriverInterface (Interface library) first
   - Build RS485FilterDriver (UMDF driver)
   - Build RS485Test (Test application)

4. **Build All**
   ```
   Build -> Build Solution (Ctrl+Shift+B)
   ```

### Method 2: Command Line Build (WDK Build Environment)

1. **Open WDK Build Environment**
   ```
   Start -> Windows Kits -> WDK -> x64 Checked Build Environment
   ```

2. **Navigate to Project Directory**
   ```
   cd /d "d:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver"
   ```

3. **Build Interface Library**
   ```
   cd Interface
   build
   cd ..
   ```

4. **Build Driver**
   ```
   cd Driver
   build
   cd ..
   ```

5. **Build Test Application**
   ```
   cd Test
   build
   cd ..
   ```

## Build Output

### Debug Build
```
Build/Debug/x64/
├── RS485FilterDriver.dll      # UMDF driver binary
├── RS485FilterDriver.inf      # Driver installation file
├── RS485DriverInterface.lib   # Interface library
├── RS485Test.exe              # Test application
└── *.pdb                      # Debug symbols
```

### Release Build
```
Build/Release/x64/
├── RS485FilterDriver.dll      # UMDF driver binary (optimized)
├── RS485FilterDriver.inf      # Driver installation file
├── RS485DriverInterface.lib   # Interface library (optimized)
├── RS485Test.exe              # Test application (optimized)
└── *.pdb                      # Debug symbols
```

## Driver Installation

### Development Testing (Test Signing)

1. **Enable Test Signing**
   ```
   bcdedit /set testsigning on
   ```
   Reboot required.

2. **Install Driver**
   ```
   Right-click on RS485FilterDriver.inf -> Install
   ```
   Or use Device Manager to update driver for FTDI device.

3. **Verify Installation**
   ```
   Device Manager -> Ports (COM & LPT) -> Look for "AI-SLDAP RS485 Filter Driver"
   ```

### Production Deployment

1. **Code Signing**
   - Obtain code signing certificate
   - Sign the driver binary and catalog file
   - Create signed driver package

2. **Distribution**
   - Create installer package
   - Include signed driver files
   - Provide installation instructions

## Testing

### Basic Functionality Test

1. **Run Test Application**
   ```
   cd Build/Debug/x64
   RS485Test.exe
   ```

2. **Expected Output**
   ```
   RS485 Driver Test Application
   =============================
   
   1. Testing Device Enumeration...
      PASSED: Device enumeration successful
   
   2. Testing Driver Connection...
      PASSED: Driver connection successful
   
   ... (additional tests)
   
   ALL TESTS PASSED!
   ```

### Hardware Testing

1. **Connect Hardware**
   - USB-RS485-WE-1800-BT converter
   - AI-SLDAP device (ZM-AISL-01)
   - Proper RS485 wiring

2. **Run Communication Tests**
   - System configuration (S-series commands)
   - User configuration (U-series commands)
   - Data requests (A-series commands)
   - Buffer management verification

## Troubleshooting

### Build Issues

**Error: WDK not found**
- Solution: Install Windows Driver Kit and ensure Visual Studio integration

**Error: Missing headers**
- Solution: Check include paths in project settings
- Verify WDK installation

**Error: Link errors**
- Solution: Check library paths and dependencies
- Ensure correct target architecture (x64/x86)

### Driver Installation Issues

**Error: Driver not signed**
- Solution: Enable test signing for development
- Use proper code signing for production

**Error: Device not recognized**
- Solution: Check INF file hardware IDs
- Verify FTDI device is connected and recognized

**Error: Access denied**
- Solution: Run as administrator
- Check user permissions

### Runtime Issues

**Error: Device not found**
- Solution: Check device enumeration
- Verify driver installation
- Check hardware connections

**Error: Communication timeout**
- Solution: Check RS485 wiring
- Verify baud rate settings
- Check slave device configuration

## Development Notes

### Code Structure
- **Driver/**: UMDF driver implementation
- **Interface/**: High-level API wrapper
- **Test/**: Test applications
- **Include/**: Shared header files

### Key Features
- UMDF 2.0 filter driver architecture
- 12-byte payload buffer management
- ZES protocol implementation
- Cross-platform data format support
- Comprehensive error handling

### API Design
- High-level abstraction of DeviceIoControl
- Automatic data type conversion
- Buffer overflow protection
- Asynchronous communication support

## Support

For technical support and questions:
- Review design document: `RS485_Driver_API_Design_Document_Updated.md`
- Check test application output for diagnostics
- Enable debug logging for detailed information
- Contact development team for assistance
