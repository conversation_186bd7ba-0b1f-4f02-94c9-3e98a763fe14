;
; RS485 Filter Driver - Development Version
; Simplified INF for development and testing
;

[Version]
Signature="$WINDOWS NT$"
Class=System
ClassGuid={4D36E97D-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
DriverVer=07/21/2025,1.0.0.0

[Manufacturer]
%ManufacturerName%=Standard,NTamd64

[Standard.NTamd64]
%RS485Device.DeviceDesc%=RS485Filter_Install, ROOT\RS485FILTER

[RS485Filter_Install.NT]
CopyFiles=UMDriverCopy

[RS485Filter_Install.NT.Services]
AddService=,0x00000002   ; Null service

[RS485Filter_Install.NT.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install_UmdfService
UmdfServiceOrder=RS485FilterDriver

[RS485Filter_Install_UmdfService]
UmdfLibraryVersion=2.15.0
ServiceBinary=%13%\RS485FilterDriver.dll

[UMDriverCopy]
RS485FilterDriver.dll

[DestinationDirs]
UMDriverCopy=13

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="RS485 Filter Driver (Development)"
DiskName="RS485 Driver Installation"
