//
// DLL Support Functions for UMDF 2.0 Driver
// Required exports for UMDF drivers
//

#include "RS485FilterDriver.h"
#include <roapi.h>

//
// DLL Can Unload Now - Required for UMDF drivers
//
STDAPI DllCanUnloadNow()
{
    return S_OK;
}

//
// DLL Get Activation Factory - Required for UMDF drivers
//
STDAPI DllGetActivationFactory(
    _In_ HSTRING activatableClassId,
    _COM_Outptr_ IActivationFactory** factory
)
{
    UNREFERENCED_PARAMETER(activatableClassId);
    UNREFERENCED_PARAMETER(factory);
    
    return CLASS_E_CLASSNOTAVAILABLE;
}
