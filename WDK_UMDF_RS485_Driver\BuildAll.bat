@echo off
REM ===================================================================
REM RS485 Driver Complete Build Script
REM Builds both the driver and installer in one step
REM ===================================================================

setlocal enabledelayedexpansion

echo ===================================================================
echo RS485 Driver Complete Build Script
echo ===================================================================
echo.
echo This script will build:
echo 1. RS485 UMDF Filter Driver (RS485FilterDriver.dll)
echo 2. RS485 Driver Installer (RS485DriverInstaller.exe)
echo.

REM Set paths
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%
set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM Check if MSBuild exists
if not exist %MSBUILD% (
    echo ERROR: MSBuild not found!
    echo Please install Visual Studio 2022 or update the path in this script.
    pause
    exit /b 1
)

echo Step 1: Building RS485 Filter Driver...
echo ===================================================================

REM Build the driver
%MSBUILD% Driver\RS485FilterDriver.vcxproj /p:Configuration=Release /p:Platform=x64 /v:minimal

if errorlevel 1 (
    echo ERROR: Driver build failed!
    pause
    exit /b 1
)

echo Driver build completed successfully.
echo.

echo Step 2: Building RS485 Driver Installer...
echo ===================================================================

REM Build the installer
%MSBUILD% Installer\RS485DriverInstaller.vcxproj /p:Configuration=Release /p:Platform=x64 /v:minimal

if errorlevel 1 (
    echo ERROR: Installer build failed!
    pause
    exit /b 1
)

echo Installer build completed successfully.
echo.

echo Step 3: Copying Final Files...
echo ===================================================================

REM Create output directory
if not exist "FinalOutput" mkdir "FinalOutput"

REM Copy final installer
copy "Build\Release\x64\RS485DriverInstaller.exe" "FinalOutput\"

REM Copy driver files for reference
copy "Driver\Build\Release\x64\RS485FilterDriver.dll" "FinalOutput\"
copy "Driver\RS485FilterDriver.inf" "FinalOutput\"

echo Files copied to FinalOutput directory.
echo.

echo ===================================================================
echo BUILD COMPLETE!
echo ===================================================================
echo.
echo Final installer: FinalOutput\RS485DriverInstaller.exe
echo.
echo IMPORTANT NOTES:
echo 1. The installer contains placeholder FTDI driver
echo 2. Download real CDM2123620_Setup.exe from FTDI website
echo 3. Replace Installer\CDM2123620_Setup.exe with real file
echo 4. Rebuild installer for production use
echo.
echo To install the driver:
echo 1. Right-click RS485DriverInstaller.exe
echo 2. Select "Run as administrator"
echo 3. Follow installation prompts
echo.

pause
