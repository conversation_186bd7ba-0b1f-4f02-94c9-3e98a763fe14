@echo off
echo ===================================================================
echo Building RS485 Driver Test Program
echo ===================================================================
echo.

REM Compile the test program
echo Compiling TestRS485Driver.cpp...

"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" ^
    /EHsc ^
    /std:c++17 ^
    /Fe:TestRS485Driver.exe ^
    TestRS485Driver.cpp ^
    /link setupapi.lib

if %errorLevel% equ 0 (
    echo ✅ Test program compiled successfully!
    echo.
    echo You can now run: .\TestRS485Driver.exe
) else (
    echo ❌ Compilation failed!
    echo.
    echo Trying alternative compilation method...
    
    REM Try using MSBuild environment
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64
    cl /EHsc /std:c++17 /Fe:TestRS485Driver.exe TestRS485Driver.cpp /link setupapi.lib
    
    if %errorLevel% equ 0 (
        echo ✅ Test program compiled successfully with alternative method!
    ) else (
        echo ❌ Both compilation methods failed.
        echo Please ensure Visual Studio 2022 is properly installed.
    )
)

echo.
pause
