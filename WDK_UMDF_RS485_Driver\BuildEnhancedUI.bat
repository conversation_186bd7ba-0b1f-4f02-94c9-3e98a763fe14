@echo off
echo ===================================================================
echo Building Enhanced RS485 Test UI Application
echo ===================================================================
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1

echo Compiling Enhanced RS485TestUI.cpp...
echo.

REM Compile the enhanced UI application
cl.exe ^
    /EHsc ^
    /std:c++17 ^
    /Fe:RS485TestUI_Enhanced.exe ^
    /DWIN32 ^
    /D_WINDOWS ^
    /DUNICODE ^
    /D_UNICODE ^
    RS485TestUI.cpp ^
    /link ^
    user32.lib ^
    gdi32.lib ^
    comctl32.lib ^
    setupapi.lib ^
    /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo ===================================================================
    echo ✅ Enhanced RS485 Test UI compiled successfully!
    echo ===================================================================
    echo.
    echo You can now run: .\RS485TestUI_Enhanced.exe
    echo.
    echo Enhanced Features:
    echo - Driver connection testing
    echo - FTDI device detection
    echo - Automatic test sequence
    echo - Progress tracking
    echo - Enhanced status display
    echo - U001 command testing
    echo.
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please ensure Visual Studio 2022 is properly installed.
)

echo.
pause
