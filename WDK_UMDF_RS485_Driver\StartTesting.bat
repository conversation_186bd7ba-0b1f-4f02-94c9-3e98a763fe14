@echo off
echo ===================================================================
echo RS485 Driver Testing - One-Click Start
echo ===================================================================
echo.

echo ✅ Your RS485 UMDF driver is already installed and working!
echo.
echo From your previous output:
echo "=== Installation Complete ==="
echo "RS485 Driver has been successfully installed!"
echo.

echo Now let's build and start the testing UI...
echo.

REM Check if UI is already built
if exist "RS485TestUI.exe" (
    echo ✅ Test UI already exists.
    echo.
    set /p REBUILD=Do you want to rebuild it? (Y/N): 
    if /i "!REBUILD!"=="Y" (
        echo Rebuilding...
        call BuildTestUI.bat
    )
) else (
    echo Building Test UI...
    call BuildTestUI.bat
)

echo.
echo ===================================================================
echo Starting RS485 Test UI
echo ===================================================================
echo.

if exist "RS485TestUI.exe" (
    echo Launching RS485 Test UI...
    echo.
    echo Instructions:
    echo 1. Connect your RS485 device to USB
    echo 2. Click "Refresh Ports" in the UI
    echo 3. Select the correct COM port
    echo 4. Set the baud rate (usually 9600)
    echo 5. Click "Connect"
    echo 6. Use the test buttons or send custom data
    echo.
    echo Starting UI now...
    start RS485TestUI.exe
    echo.
    echo ✅ UI launched! Check the new window.
) else (
    echo ❌ Failed to build or find RS485TestUI.exe
    echo Please check the build output above for errors.
)

echo.
echo ===================================================================
echo Testing Guide
echo ===================================================================
echo.
echo Your final deliverable: .\FinalOutput\RS485DriverInstaller.exe
echo - Single EXE file containing FTDI + RS485 UMDF driver
echo - Built with WDK and UMDF 2.0
echo - Works on top of FTDI driver as filter driver
echo.
echo For detailed testing instructions, see: UI_TEST_GUIDE.md
echo.

pause
