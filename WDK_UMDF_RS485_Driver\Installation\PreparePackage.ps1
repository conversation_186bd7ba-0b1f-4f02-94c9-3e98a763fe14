#
# AI-SLDAP RS485 Driver Package Preparation Script
# Builds the driver and creates a complete installation package
#
# This script must be run from the project root directory

param(
    [string]$Configuration = "Debug",
    [string]$Platform = "x64",
    [string]$OutputPath = ".\DriverPackage",
    [switch]$Clean = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI-SLDAP RS485 Driver Package Builder" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "RS485Driver.sln")) {
    Write-Host "ERROR: This script must be run from the project root directory!" -ForegroundColor Red
    Write-Host "Expected to find: RS485Driver.sln" -ForegroundColor Yellow
    exit 1
}

# Function to find MSBuild
function Find-MSBuild {
    $msbuildPaths = @(
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    foreach ($path in $msbuildPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    
    # Try to find in PATH
    $msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
    if ($msbuild) {
        return $msbuild.Source
    }
    
    return $null
}

# Find MSBuild
$msbuildPath = Find-MSBuild
if (-not $msbuildPath) {
    Write-Host "ERROR: MSBuild not found!" -ForegroundColor Red
    Write-Host "Please install Visual Studio 2019 or 2022 with C++ build tools." -ForegroundColor Yellow
    exit 1
}

Write-Host "Using MSBuild: $msbuildPath" -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Green
Write-Host "Output Path: $OutputPath" -ForegroundColor Green
Write-Host ""

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    & "$msbuildPath" "RS485Driver.sln" /p:Configuration=$Configuration /p:Platform=$Platform /t:Clean /v:minimal
    
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
        Write-Host "Cleaned output directory: $OutputPath" -ForegroundColor Green
    }
    Write-Host ""
}

# Build the driver
Write-Host "Building RS485 Filter Driver..." -ForegroundColor Yellow
& "$msbuildPath" "Driver\RS485FilterDriver.vcxproj" /p:Configuration=$Configuration /p:Platform=$Platform /v:minimal

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Driver build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Driver build completed successfully." -ForegroundColor Green
Write-Host ""

# Build the test application
Write-Host "Building Test Application..." -ForegroundColor Yellow
& "$msbuildPath" "Test\SimpleDriverTest.vcxproj" /p:Configuration=$Configuration /p:Platform=$Platform /v:minimal

if ($LASTEXITCODE -ne 0) {
    Write-Host "WARNING: Test application build failed!" -ForegroundColor Yellow
    Write-Host "Continuing with driver package creation..." -ForegroundColor Yellow
} else {
    Write-Host "Test application build completed successfully." -ForegroundColor Green
}
Write-Host ""

# Create output directory structure
Write-Host "Creating package directory structure..." -ForegroundColor Yellow

$packageDirs = @(
    $OutputPath,
    "$OutputPath\TestApplication",
    "$OutputPath\Documentation"
)

foreach ($dir in $packageDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# Copy driver files
Write-Host "Copying driver files..." -ForegroundColor Yellow

$driverFiles = @{
    "Driver\$Platform\$Configuration\RS485FilterDriver.dll" = "$OutputPath\RS485FilterDriver.dll"
    "Driver\RS485FilterDriver.inf" = "$OutputPath\RS485FilterDriver.inf"
}

foreach ($source in $driverFiles.Keys) {
    $destination = $driverFiles[$source]
    
    if (Test-Path $source) {
        Copy-Item $source $destination -Force
        Write-Host "Copied: $source → $destination" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Source file not found: $source" -ForegroundColor Yellow
    }
}

# Copy installation scripts
Write-Host "Copying installation scripts..." -ForegroundColor Yellow

$installationFiles = @(
    "Installation\InstallDriver.bat",
    "Installation\InstallDriver.ps1",
    "Installation\UninstallDriver.ps1",
    "Installation\INSTALLATION_GUIDE.md"
)

foreach ($file in $installationFiles) {
    if (Test-Path $file) {
        $filename = Split-Path $file -Leaf
        Copy-Item $file "$OutputPath\$filename" -Force
        Write-Host "Copied: $file → $OutputPath\$filename" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Installation file not found: $file" -ForegroundColor Yellow
    }
}

# Copy test application
if (Test-Path "Test\$Platform\$Configuration\SimpleDriverTest.exe") {
    Copy-Item "Test\$Platform\$Configuration\SimpleDriverTest.exe" "$OutputPath\TestApplication\" -Force
    Write-Host "Copied: Test application → $OutputPath\TestApplication\" -ForegroundColor Green
} else {
    Write-Host "WARNING: Test application not found" -ForegroundColor Yellow
}

# Copy documentation
Write-Host "Copying documentation..." -ForegroundColor Yellow

$docFiles = @(
    "README.md",
    "PROJECT_SUMMARY.md",
    "DETAILED_BUILD_GUIDE.md"
)

foreach ($file in $docFiles) {
    if (Test-Path $file) {
        $filename = Split-Path $file -Leaf
        Copy-Item $file "$OutputPath\Documentation\$filename" -Force
        Write-Host "Copied: $file → $OutputPath\Documentation\$filename" -ForegroundColor Green
    }
}

# Create package information file
Write-Host "Creating package information..." -ForegroundColor Yellow

$packageInfo = @"
AI-SLDAP RS485 Driver Package
=============================

Build Information:
- Configuration: $Configuration
- Platform: $Platform
- Build Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- Build Machine: $env:COMPUTERNAME
- Build User: $env:USERNAME

Package Contents:
- RS485FilterDriver.dll    - UMDF 2.0 Filter Driver
- RS485FilterDriver.inf    - Driver Installation Information
- InstallDriver.ps1        - PowerShell Installation Script (Recommended)
- InstallDriver.bat        - Batch Installation Script
- UninstallDriver.ps1      - PowerShell Uninstallation Script
- INSTALLATION_GUIDE.md    - Complete Installation Guide
- TestApplication/         - Test Application and Tools
- Documentation/           - Additional Documentation

Installation Requirements:
- Windows 10/11 (x64 or x86)
- Administrator privileges
- FTDI VCP Driver (CDM2123620_Setup.exe) - Must be provided separately
- FTDI-based RS485 USB adapter

Quick Start:
1. Copy CDM2123620_Setup.exe to this directory
2. Run PowerShell as Administrator
3. Execute: .\InstallDriver.ps1
4. Connect RS485 device and test with TestApplication\SimpleDriverTest.exe

For detailed instructions, see INSTALLATION_GUIDE.md
"@

$packageInfo | Out-File "$OutputPath\PACKAGE_INFO.txt" -Encoding UTF8
Write-Host "Created: PACKAGE_INFO.txt" -ForegroundColor Green

# Create a simple README for the package
$packageReadme = @"
# AI-SLDAP RS485 Driver Package

This package contains the complete AI-SLDAP RS485 driver solution.

## Quick Installation

1. **Copy FTDI Driver**: Place `CDM2123620_Setup.exe` in this directory
2. **Run as Administrator**: Right-click PowerShell → "Run as administrator"
3. **Install**: Run `.\InstallDriver.ps1`

## What's Included

- Complete UMDF 2.0 RS485 filter driver
- Installation and uninstallation scripts
- Test application for verification
- Comprehensive documentation

## Support

See `INSTALLATION_GUIDE.md` for detailed instructions and troubleshooting.

---
Built on $(Get-Date -Format "yyyy-MM-dd") | Configuration: $Configuration | Platform: $Platform
"@

$packageReadme | Out-File "$OutputPath\README.md" -Encoding UTF8
Write-Host "Created: README.md" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Package Creation Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Package Location: $OutputPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Copy CDM2123620_Setup.exe to the package directory" -ForegroundColor White
Write-Host "2. Test the installation on a clean system" -ForegroundColor White
Write-Host "3. Distribute the complete package to users" -ForegroundColor White
Write-Host ""
Write-Host "Package is ready for distribution!" -ForegroundColor Green
