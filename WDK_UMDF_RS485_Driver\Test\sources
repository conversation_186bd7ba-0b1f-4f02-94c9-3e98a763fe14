#
# RS485 Test Application Build Configuration
# Test application for RS485 driver interface
#

TARGETNAME=RS485Test
TARGETTYPE=PROGRAM

# Target platform
_NT_TARGET_VERSION=$(_NT_TARGET_VERSION_WIN10)

# Use standard C runtime
USE_MSVCRT=1

# Source files
SOURCES=\
    RS485Test.cpp

# Include directories
INCLUDES=\
    ..\Include; \
    ..\Interface; \
    $(SDK_INC_PATH)

# Target libraries
TARGETLIBS=\
    $(SDK_LIB_PATH)\kernel32.lib \
    $(SDK_LIB_PATH)\user32.lib \
    $(SDK_LIB_PATH)\setupapi.lib \
    ..\Interface\$(O)\RS485DriverInterface.lib

# Compiler definitions
C_DEFINES=$(C_DEFINES) -DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN

# Warning level
MSC_WARNING_LEVEL=/W4

# Debug settings
!if "$(DDKBUILDENV)" == "chk"
C_DEFINES=$(C_DEFINES) -DDBG=1
!endif

# C++ standard
USE_STL=1
STL_VER=100

# Additional compiler flags
USER_C_FLAGS=/std:c++17

# Console subsystem
UMTYPE=console
