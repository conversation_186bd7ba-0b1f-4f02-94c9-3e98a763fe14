//
// Simple RS485 Driver Test Application
// Tests basic driver functionality without the complex interface library
//

#include <windows.h>
#include <iostream>
#include <string>
#include <setupapi.h>
#include <devguid.h>

// Include the driver's IOCTL definitions
#include "../Include/RS485InterfaceCommon.h"

#pragma comment(lib, "setupapi.lib")

class SimpleRS485Test {
private:
    HANDLE m_driverHandle;
    std::string m_devicePath;

public:
    SimpleRS485Test() : m_driverHandle(INVALID_HANDLE_VALUE) {}
    
    ~SimpleRS485Test() {
        if (m_driverHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(m_driverHandle);
        }
    }

    bool FindDriver() {
        // Try to find the RS485 filter driver
        HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
            nullptr,
            L"ROOT\\RS485FilterDriver",
            nullptr,
            DIGCF_PRESENT | DIGCF_ALLCLASSES
        );

        if (deviceInfoSet == INVALID_HANDLE_VALUE) {
            std::cout << "Failed to get device info set. Error: " << GetLastError() << std::endl;
            return false;
        }

        SP_DEVINFO_DATA deviceInfoData = { sizeof(SP_DEVINFO_DATA) };
        
        for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
            WCHAR devicePath[MAX_PATH];
            DWORD requiredSize = 0;
            
            if (SetupDiGetDeviceRegistryProperty(
                deviceInfoSet,
                &deviceInfoData,
                SPDRP_PHYSICAL_DEVICE_OBJECT_NAME,
                nullptr,
                (PBYTE)devicePath,
                sizeof(devicePath),
                &requiredSize)) {
                
                // Convert to string and store
                char narrowPath[MAX_PATH];
                WideCharToMultiByte(CP_ACP, 0, devicePath, -1, narrowPath, MAX_PATH, nullptr, nullptr);
                m_devicePath = std::string("\\\\.\\") + narrowPath;
                
                std::cout << "Found device: " << m_devicePath << std::endl;
                SetupDiDestroyDeviceInfoList(deviceInfoSet);
                return true;
            }
        }

        SetupDiDestroyDeviceInfoList(deviceInfoSet);
        
        // If not found, try a direct path
        m_devicePath = "\\\\.\\RS485FilterDriver";
        std::cout << "Using direct device path: " << m_devicePath << std::endl;
        return true;
    }

    bool OpenDriver() {
        if (m_devicePath.empty()) {
            std::cout << "No device path available" << std::endl;
            return false;
        }

        m_driverHandle = CreateFileA(
            m_devicePath.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (m_driverHandle == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            std::cout << "Failed to open driver. Error: " << error << std::endl;
            return false;
        }

        std::cout << "Successfully opened driver!" << std::endl;
        return true;
    }

    bool TestBasicIOCTL() {
        if (m_driverHandle == INVALID_HANDLE_VALUE) {
            std::cout << "Driver not open" << std::endl;
            return false;
        }

        // Test getting driver status - using a simple buffer since we don't have the exact structure
        BYTE statusBuffer[256] = { 0 };
        DWORD bytesReturned = 0;

        BOOL result = DeviceIoControl(
            m_driverHandle,
            IOCTL_RS485_GET_STATUS,
            nullptr,
            0,
            statusBuffer,
            sizeof(statusBuffer),
            &bytesReturned,
            nullptr
        );

        if (!result) {
            DWORD error = GetLastError();
            std::cout << "IOCTL_RS485_GET_STATUS failed. Error: " << error << std::endl;
            return false;
        }

        std::cout << "Driver status retrieved successfully!" << std::endl;
        std::cout << "  Bytes returned: " << bytesReturned << std::endl;

        return true;
    }

    bool TestBufferStatus() {
        if (m_driverHandle == INVALID_HANDLE_VALUE) {
            std::cout << "Driver not open" << std::endl;
            return false;
        }

        // Test getting buffer status - using a simple buffer since we don't have the exact structure
        BYTE bufferStatusBuffer[256] = { 0 };
        DWORD bytesReturned = 0;

        BOOL result = DeviceIoControl(
            m_driverHandle,
            IOCTL_RS485_GET_BUFFER_STATUS,
            nullptr,
            0,
            bufferStatusBuffer,
            sizeof(bufferStatusBuffer),
            &bytesReturned,
            nullptr
        );

        if (!result) {
            DWORD error = GetLastError();
            std::cout << "IOCTL_RS485_GET_BUFFER_STATUS failed. Error: " << error << std::endl;
            return false;
        }

        std::cout << "Buffer status retrieved successfully!" << std::endl;
        std::cout << "  Bytes returned: " << bytesReturned << std::endl;

        return true;
    }

    void RunAllTests() {
        std::cout << "=== RS485 Driver Test Application ===" << std::endl;
        
        if (!FindDriver()) {
            std::cout << "Failed to find driver" << std::endl;
            return;
        }

        if (!OpenDriver()) {
            std::cout << "Failed to open driver" << std::endl;
            return;
        }

        std::cout << "\n--- Testing Basic IOCTL ---" << std::endl;
        TestBasicIOCTL();

        std::cout << "\n--- Testing Buffer Status ---" << std::endl;
        TestBufferStatus();

        std::cout << "\n=== Test Complete ===" << std::endl;
    }
};

int main() {
    SimpleRS485Test test;
    test.RunAllTests();
    
    std::cout << "\nPress Enter to exit...";
    std::cin.get();
    return 0;
}
