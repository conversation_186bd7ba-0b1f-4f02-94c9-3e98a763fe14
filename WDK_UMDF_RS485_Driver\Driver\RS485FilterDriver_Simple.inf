;
; RS485 Filter Driver - Simplified Development Version
; Minimal INF for development testing without catalog requirements
;

[Version]
Signature="$WINDOWS NT$"
Class=System
ClassGuid={4D36E97D-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
DriverVer=07/21/2025,1.0.0.0

[Manufacturer]
%ManufacturerName%=Standard,NTamd64

[Standard.NTamd64]
%RS485Device.DeviceDesc%=RS485Filter_Install, ROOT\RS485FILTER

[RS485Filter_Install]
; Minimal installation section

[RS485Filter_Install.Services]
AddService=,0x00000002   ; Null service

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="RS485 Filter Driver (Development)"
DiskName="RS485 Driver Installation"
