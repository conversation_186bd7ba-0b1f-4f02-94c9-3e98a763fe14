@echo off
REM AI-SLDAP RS485 Driver Installation Script
REM Integrates FTDI VCP Driver with UMDF RS485 Filter Driver
REM
REM This script must be run as Administrator

echo ========================================
echo AI-SLDAP RS485 Driver Installation
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Checking system requirements...

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

REM Check architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=x64
    echo Architecture: 64-bit
) else (
    set ARCH=x86
    echo Architecture: 32-bit
)

echo.
echo ========================================
echo Step 1: Installing FTDI VCP Driver
echo ========================================

REM Check if FTDI driver installer exists
if not exist "CDM2123620_Setup.exe" (
    echo ERROR: FTDI driver installer not found!
    echo Please ensure CDM2123620_Setup.exe is in the same directory.
    pause
    exit /b 1
)

echo Installing FTDI VCP Driver...
echo This may take a few minutes and may require a reboot.
echo.

REM Install FTDI driver silently
start /wait CDM2123620_Setup.exe /S

if %errorLevel% neq 0 (
    echo WARNING: FTDI driver installation may have failed.
    echo Please check if the driver was installed correctly.
    pause
)

echo FTDI VCP Driver installation completed.
echo.

echo ========================================
echo Step 2: Installing RS485 Filter Driver
echo ========================================

REM Check if our driver files exist
if not exist "RS485FilterDriver.dll" (
    echo ERROR: RS485FilterDriver.dll not found!
    echo Please ensure all driver files are present.
    pause
    exit /b 1
)

if not exist "RS485FilterDriver.inf" (
    echo ERROR: RS485FilterDriver.inf not found!
    echo Please ensure all driver files are present.
    pause
    exit /b 1
)

echo Installing AI-SLDAP RS485 Filter Driver...

REM Install our driver using pnputil
pnputil /add-driver RS485FilterDriver.inf /install

if %errorLevel% neq 0 (
    echo ERROR: Failed to install RS485 Filter Driver!
    echo Please check the driver files and try again.
    pause
    exit /b 1
)

echo RS485 Filter Driver installation completed.
echo.

echo ========================================
echo Step 3: Verifying Installation
echo ========================================

echo Checking installed drivers...

REM List installed drivers
pnputil /enum-drivers | findstr /i "RS485"

echo.
echo Checking device manager for FTDI devices...

REM Check if FTDI devices are present
devcon find "USB\VID_0403*" 2>nul
if %errorLevel% neq 0 (
    echo No FTDI devices currently connected.
    echo Please connect your RS485 device to complete setup.
)

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo The AI-SLDAP RS485 Driver has been successfully installed.
echo.
echo Next steps:
echo 1. Connect your RS485 device via USB
echo 2. Windows will automatically detect and configure the device
echo 3. Use the provided test application to verify functionality
echo.
echo If you encounter any issues:
echo - Check Device Manager for any warning signs
echo - Ensure your RS485 device is properly connected
echo - Run the test application as Administrator
echo.

pause
echo Installation script completed.
