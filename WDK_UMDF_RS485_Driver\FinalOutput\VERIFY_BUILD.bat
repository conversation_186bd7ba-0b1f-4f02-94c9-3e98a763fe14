@echo off
echo ===================================================================
echo RS485 Driver Final Build Verification
echo ===================================================================
echo.

echo Checking required files...
echo.

REM Check driver files
if exist "RS485FilterDriver.dll" (
    echo ✅ RS485FilterDriver.dll - UMDF Driver [FOUND]
) else (
    echo ❌ RS485FilterDriver.dll - UMDF Driver [MISSING]
)

if exist "RS485FilterDriver.inf" (
    echo ✅ RS485FilterDriver.inf - Driver Info [FOUND]
) else (
    echo ❌ RS485FilterDriver.inf - Driver Info [MISSING]
)

if exist "RS485DriverInstaller.exe" (
    echo ✅ RS485DriverInstaller.exe - Complete Installer [FOUND]
) else (
    echo ❌ RS485DriverInstaller.exe - Complete Installer [MISSING]
)

if exist "RS485TestUI_Complete.exe" (
    echo ✅ RS485TestUI_Complete.exe - Basic Test UI [FOUND]
) else (
    echo ❌ RS485TestUI_Complete.exe - Basic Test UI [MISSING]
)

if exist "RS485TestUI_Enhanced_Final.exe" (
    echo ✅ RS485TestUI_Enhanced_Final.exe - ENHANCED Test UI [FOUND]
) else (
    echo ❌ RS485TestUI_Enhanced_Final.exe - ENHANCED Test UI [MISSING]
)

echo.
echo ===================================================================
echo File Size Information
echo ===================================================================
echo.

for %%f in (*.dll *.exe *.inf) do (
    echo %%f - File exists
)

echo.
echo ===================================================================
echo Build Verification Complete
echo ===================================================================
echo.

echo Ready for deployment! All required files are present.
echo.
echo Next steps:
echo 1. Run RS485DriverInstaller.exe as Administrator to install driver
echo 2. Launch RS485TestUI_Enhanced_Final.exe (RECOMMENDED - with auto-detection)
echo 3. Click "Auto Connect" to automatically detect RS485 device
echo 4. Test all S001, S002, U001-U006 commands with parameter inputs
echo 5. Use "Test All Commands" to send complete command sequence
echo.

pause
