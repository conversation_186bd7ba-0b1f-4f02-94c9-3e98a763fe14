@echo off
setlocal enabledelayedexpansion

echo ===================================================================
echo RS485 Driver Signing Tool - Fixed Version
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if !errorLevel! neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Enabling test signing mode...
echo ===================================================================

bcdedit /set testsigning on
if !errorLevel! equ 0 (
    echo Test signing enabled successfully.
) else (
    echo WARNING: Failed to enable test signing.
)

echo.
echo Step 2: Creating test certificate...
echo ===================================================================

REM Create certificate using PowerShell
powershell -ExecutionPolicy Bypass -Command "& { try { $cert = New-SelfSignedCertificate -Subject 'CN=RS485TestCert' -CertStoreLocation 'Cert:\CurrentUser\My' -KeyUsage DigitalSignature -Type CodeSigningCert; Write-Host 'Certificate created successfully'; Export-Certificate -Cert $cert -FilePath 'RS485TestCert.cer' -Force } catch { Write-Host 'Error:' $_.Exception.Message } }"

echo.
echo Step 3: Installing certificate to trusted stores...
echo ===================================================================

if exist "RS485TestCert.cer" (
    certlm -add "RS485TestCert.cer" -s -r localMachine root
    certlm -add "RS485TestCert.cer" -s -r localMachine trustedpublisher
    echo Certificate installed.
) else (
    echo WARNING: Certificate file not created.
)

echo.
echo Step 4: Creating catalog file...
echo ===================================================================

if exist "Driver\RS485FilterDriver.inf" (
    pushd Driver
    inf2cat /driver:. /os:10_X64 2>nul
    if !errorLevel! equ 0 (
        echo Catalog file created successfully.
    ) else (
        echo INFO: Catalog file creation skipped (inf2cat not available or failed).
    )
    popd
) else (
    echo WARNING: INF file not found.
)

echo.
echo Step 5: Signing driver files...
echo ===================================================================

if exist "Driver\Build\Release\x64\RS485FilterDriver.dll" (
    signtool sign /v /s My /n "RS485TestCert" /fd SHA256 "Driver\Build\Release\x64\RS485FilterDriver.dll" 2>nul
    if !errorLevel! equ 0 (
        echo Driver DLL signed successfully.
    ) else (
        echo INFO: Driver DLL signing skipped (signtool not available or failed).
    )
) else (
    echo INFO: Driver DLL not found (build the driver first).
)

if exist "Driver\RS485FilterDriver.cat" (
    signtool sign /v /s My /n "RS485TestCert" /fd SHA256 "Driver\RS485FilterDriver.cat" 2>nul
    if !errorLevel! equ 0 (
        echo Catalog file signed successfully.
    ) else (
        echo INFO: Catalog file signing skipped.
    )
)

echo.
echo ===================================================================
echo Process Complete
echo ===================================================================
echo.
echo Summary:
echo - Test signing mode: ENABLED
echo - Test certificate: Created and installed
echo - Driver files: Processed
echo.
echo IMPORTANT: You must REBOOT your computer for test signing to take effect!
echo.
echo After reboot, run: .\FinalOutput\RS485DriverInstaller.exe
echo.

pause
