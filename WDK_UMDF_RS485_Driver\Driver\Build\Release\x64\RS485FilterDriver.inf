;
; RS485 Filter Driver - UMDF 2.0 Upper Filter
; Integrates with FTDI VCP Driver for RS485 Protocol Support
;

[Version]
Signature="$WINDOWS NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
CatalogFile=RS485FilterDriver.cat
DriverVer=07/21/2025,1.0.0.0
PnpLockdown=1

[Manufacturer]
%ManufacturerName%=Standard,NTamd64

[Standard.NTamd64]
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6001
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6015
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6010
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6011

[RS485Filter_Install.NT]
CopyFiles=UMDriverCopy

[RS485Filter_Install.NT.Services]
AddService=,0x00000002   ; Null service - UMDF driver

[RS485Filter_Install.NT.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install_UmdfService
UmdfServiceOrder=RS485FilterDriver
UmdfKernelModeClientPolicy=AllowKernelModeClients
UmdfFileObjectPolicy=AllowNullAndUnknownFileObjects
UmdfFsContextUsePolicy=CanUseFsContext2

[RS485Filter_Install_UmdfService]
UmdfLibraryVersion=2.15.0
ServiceBinary=%13%\RS485FilterDriver.dll
UmdfDispatcher=FileHandle
UmdfImpersonationLevel=Impersonation

[RS485Filter_Install.NT.HW]
AddReg=RS485Filter_AddReg

[RS485Filter_AddReg]
HKR,,"DeviceInterfaceGUIDs",0x00010000,"{A5DCBF10-6530-11D2-901F-00C04FB951ED}"
HKR,,"System.Devices.InterfaceClassGuid",0x00000000,"{A5DCBF10-6530-11D2-901F-00C04FB951ED}"

[UMDriverCopy]
RS485FilterDriver.dll

[DestinationDirs]
UMDriverCopy=13

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="AI-SLDAP RS485 Communication Driver"
DiskName="AI-SLDAP RS485 Driver Installation Disk"
