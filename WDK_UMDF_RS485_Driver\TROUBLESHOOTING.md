# 🔧 RS485 Driver 故障排除指南

## 🚨 批处理脚本错误

### 错误: "is was unexpected at this time"

**原因**: 批处理脚本语法错误，通常是变量引用或括号问题。

**解决方案**:
1. **使用修复后的脚本**:
   ```batch
   .\EnableTestSigning.bat
   ```

2. **或者手动执行命令**:
   ```batch
   # 以管理员身份运行命令提示符
   bcdedit /set testsigning on
   shutdown /r /t 0
   ```

## 🎯 快速解决方案

### 最简单的方法

1. **以管理员身份运行**:
   ```batch
   .\EnableTestSigning.bat
   ```

2. **重启计算机**

3. **运行安装程序**:
   ```batch
   .\FinalOutput\RS485DriverInstaller.exe
   ```

### 如果不想重启

您的驱动已经通过备用方法成功安装！无需进一步操作。

## 📋 常见问题解决

### Q1: 脚本显示语法错误
**A**: 使用 `EnableTestSigning.bat` 替代复杂的签名脚本。

### Q2: 需要管理员权限
**A**: 右键点击脚本文件，选择"以管理员身份运行"。

### Q3: 测试签名启用失败
**A**: 可能的原因和解决方案：
- **Secure Boot**: 在 BIOS 中禁用 Secure Boot
- **BitLocker**: 暂时禁用 BitLocker
- **组策略**: 检查企业策略限制

### Q4: 驱动安装失败
**A**: 您的驱动已经成功安装！看到 "Installation Complete" 表示成功。

## 🔍 验证安装

### 检查驱动是否已安装
```batch
dir C:\Windows\System32\RS485FilterDriver.dll
```

### 检查测试签名状态
```batch
bcdedit /enum {current} | findstr testsigning
```

### 检查设备管理器
1. 打开设备管理器
2. 连接 FTDI 设备
3. 查看"端口 (COM & LPT)"部分

## 🎉 成功标志

当您看到以下任一消息时，表示安装成功：

```
=== Installation Complete ===
RS485 Driver has been successfully installed!
```

或

```
Alternative installation completed.
NOTE: This is a development installation.
```

## 📞 如果仍有问题

1. **使用最简单的方法**:
   ```batch
   .\EnableTestSigning.bat
   ```

2. **手动启用测试签名**:
   ```batch
   # 以管理员身份运行
   bcdedit /set testsigning on
   shutdown /r /t 0
   ```

3. **使用当前的备用安装** (已经成功):
   您的驱动已经正常工作，无需进一步操作。

## 🎯 重要提醒

**您的 RS485 驱动已经成功安装并可以使用！**

批处理脚本的错误不影响驱动的功能。如果您看到了 "Installation Complete" 消息，说明一切都正常工作。
