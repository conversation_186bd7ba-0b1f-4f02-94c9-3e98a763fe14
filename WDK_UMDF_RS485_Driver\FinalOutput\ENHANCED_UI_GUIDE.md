# RS485 Enhanced Test UI - 使用指南

## 🚀 **新功能概述**

### ✅ **已解决的问题**
1. **自动COM端口检测** - 不再需要手动选择端口
2. **参数输入界面** - 为所有8个命令提供专用的参数输入控件
3. **连接状态修复** - 只有真正连接成功才显示"Connected"
4. **按钮崩溃修复** - 所有测试按钮现在都能正常工作

### 🎯 **增强功能**
- **智能设备检测**: 自动识别FTDI/RS485设备
- **参数验证**: 实时验证输入参数范围
- **可视化界面**: 清晰的参数输入控件布局
- **状态反馈**: 详细的操作状态和错误信息

## 📋 **8个命令的参数设置**

### **系统配置命令 (S-Series)**

#### **S001 - 从站地址设置**
- **输入方式**: 文本输入框
- **参数范围**: 1-31
- **默认值**: 5
- **验证**: 自动检查范围，超出范围会显示错误

#### **S002 - 波特率设置**
- **输入方式**: 下拉选择框
- **可选值**: 9600, 19200, 38400, 57600, 115200
- **默认值**: 115200
- **验证**: 只能选择预设值

### **用户配置命令 (U-Series)**

#### **U001 - SEL检测阈值**
- **输入方式**: 文本输入框
- **参数范围**: 40-500 毫安
- **默认值**: 250
- **验证**: 自动检查范围

#### **U002 - SEL最大幅度阈值**
- **输入方式**: 文本输入框
- **参数范围**: 1000-2000 毫安
- **默认值**: 1500
- **验证**: 自动检查范围

#### **U003 - 断电前SEL检测次数**
- **输入方式**: 下拉选择框
- **可选值**: 1, 2, 3, 4, 5
- **默认值**: 3
- **验证**: 只能选择预设值

#### **U004 - 断电持续时间**
- **输入方式**: 下拉选择框
- **可选值**: 200, 400, 600, 800, 1000 毫秒
- **默认值**: 600
- **验证**: 只能选择预设值

#### **U005 - GPIO输入功能**
- **输入方式**: 双下拉选择框
  - **通道**: 0 或 1
  - **使能**: Disable 或 Enable
- **默认值**: 通道0, Enable
- **验证**: 只能选择预设值

#### **U006 - GPIO输出功能**
- **输入方式**: 双下拉选择框
  - **通道**: 0 或 1
  - **使能**: Disable 或 Enable
- **默认值**: 通道1, Enable
- **验证**: 只能选择预设值

## 🔧 **使用步骤**

### **1. 启动应用程序**
```cmd
RS485TestUI_Enhanced_Final.exe
```

### **2. 自动连接RS485设备**
1. 确保RS485设备已连接到计算机
2. 点击 **"Auto Connect"** 按钮
3. 程序会自动：
   - 扫描所有COM端口
   - 识别FTDI/RS485设备
   - 尝试连接并验证通信
   - 显示连接状态

### **3. 测试单个命令**
1. 在相应的参数输入控件中设置值
2. 点击对应的 **"Send Sxxx"** 或 **"Send Uxxx"** 按钮
3. 查看发送的十六进制数据
4. 观察状态反馈和响应

### **4. 批量测试所有命令**
1. 设置所有命令的参数值
2. 点击 **"Test All Commands"** 按钮
3. 程序会按顺序发送所有8个命令
4. 每个命令之间有100ms延迟

### **5. 断开连接**
- 点击 **"Disconnect"** 按钮安全断开连接

## 🎯 **界面布局**

```
┌─────────────────────────────────────────────────────────┐
│ RS485 Connection                                        │
│ [Auto Connect] [Disconnect]                             │
│ Status: Connected to COM3 - Ready for testing          │
├─────────────────────────────────────────────────────────┤
│ System Configuration Commands (S-Series)               │
│ S001 - Slave Address (1-31): [5    ] [Send S001]      │
│ S002 - Baud Rate: [115200 ▼] [Send S002]              │
├─────────────────────────────────────────────────────────┤
│ User Configuration Commands (U-Series)                 │
│ U001 - SEL Threshold (40-500 mA): [250  ] [Send U001] │
│ U002 - Max Amplitude (1000-2000 mA): [1500] [Send U002]│
│ U003 - Detection Count (1-5): [3 ▼] [Send U003]       │
│ U004 - Power Cycle (ms): [600 ▼] [Send U004]          │
│ U005 - GPIO Input: Ch:[0▼] En:[Enable▼] [Send U005]   │
│ U006 - GPIO Output: Ch:[1▼] En:[Enable▼] [Send U006]  │
├─────────────────────────────────────────────────────────┤
│ [Test All Commands]                                     │
│ Send Data (Hex): [AA 01 53 30 30 31 05 00 00 00...]   │
│ Received Data & Results:                                │
│ [12:34:56] Auto-detecting RS485 COM ports              │
│ [12:34:57] ✓ Found FTDI/RS485 devices: COM3           │
│ [12:34:58] ✓ Successfully connected to COM3           │
│ [12:35:00] Sending S001 command with value: 5         │
│ [12:35:01] ✓ Command sent successfully (16 bytes)     │
└─────────────────────────────────────────────────────────┘
```

## 🔍 **故障排除**

### **连接问题**
- **"No FTDI/RS485 devices found"**: 检查设备连接和驱动安装
- **"Could not connect to any RS485 port"**: 检查端口是否被其他程序占用

### **参数验证错误**
- **"S001 value must be between 1-31"**: 输入有效的从站地址
- **"U001 value must be between 40-500 mA"**: 输入有效的阈值范围

### **通信问题**
- **"No response received"**: FPGA可能尚未准备好，这是正常的
- **"Failed to send command"**: 检查连接状态

## 📞 **技术支持**

此增强版UI已完全解决了原版本的所有问题：
- ✅ 自动COM端口检测
- ✅ 参数输入界面
- ✅ 连接状态修复
- ✅ 按钮功能修复
- ✅ 实时参数验证
- ✅ 详细状态反馈

现在可以安全地用于RS485设备测试和FPGA集成验证！
