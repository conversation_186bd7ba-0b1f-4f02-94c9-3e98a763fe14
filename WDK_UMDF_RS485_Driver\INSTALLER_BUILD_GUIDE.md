# RS485 Driver Installer Build Guide

## 🎯 **目标：创建单一 .exe 安装程序**

这个指南将帮助您创建一个**单一的 .exe 安装程序**，用户运行后自动安装：
1. **FTDI VCP 驱动** (CDM2123620_Setup.exe)
2. **RS485 UMDF 过滤驱动** (RS485FilterDriver.dll)
3. **所有必要的配置和注册**

## 📋 **准备工作**

### 必需软件
1. **Visual Studio 2022** (Community/Professional/Enterprise)
2. **Windows Driver Kit (WDK) 10**
3. **Windows SDK 10.0.26100.0** 或更新版本

### 必需文件
1. **CDM2123620_Setup.exe** - FTDI VCP 驱动程序
   - 下载地址: https://ftdichip.com/drivers/vcp-drivers/
   - 放置位置: `WDK_UMDF_RS485_Driver\CDM2123620_Setup.exe`

## 🚀 **构建步骤**

### 方法 1: 自动化构建 (推荐)

1. **下载 FTDI 驱动**
   ```
   从 FTDI 官网下载 CDM2123620_Setup.exe
   放置到: WDK_UMDF_RS485_Driver\CDM2123620_Setup.exe
   ```

2. **运行构建脚本**
   ```batch
   cd WDK_UMDF_RS485_Driver\Scripts
   BuildAndPackage.bat
   ```

3. **获取最终安装程序**
   ```
   最终安装程序: WDK_UMDF_RS485_Driver\Package\RS485DriverInstaller.exe
   ```

### 方法 2: Visual Studio 构建

1. **打开解决方案**
   ```
   打开: WDK_UMDF_RS485_Driver\RS485DriverSolution.sln
   ```

2. **设置构建配置**
   ```
   Configuration: Release
   Platform: x64
   ```

3. **构建解决方案**
   ```
   Build → Build Solution (Ctrl+Shift+B)
   ```

### 方法 3: 手动构建

1. **构建驱动程序**
   ```batch
   cd WDK_UMDF_RS485_Driver\Driver
   "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" RS485FilterDriver.vcxproj /p:Configuration=Release /p:Platform=x64
   ```

2. **创建目录文件**
   ```batch
   cd WDK_UMDF_RS485_Driver\Driver
   "C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\Inf2Cat.exe" /driver:. /os:10_X64
   ```

3. **构建安装程序**
   ```batch
   cd WDK_UMDF_RS485_Driver\Installer
   "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" RS485DriverInstaller.vcxproj /p:Configuration=Release /p:Platform=x64
   ```

## 📦 **安装程序使用**

### 最终用户安装

1. **以管理员身份运行**
   ```
   右键点击 RS485DriverInstaller.exe
   选择 "以管理员身份运行"
   ```

2. **自动安装过程**
   - 安装程序会自动安装 FTDI 驱动
   - 安装程序会安装 RS485 过滤驱动
   - 无需用户交互（静默安装）

### 安装内容

1. **FTDI VCP 驱动**
   - 提供基本的 USB 转串口功能
   - 安装 COM 端口驱动
   - 注册 FTDI 设备支持

2. **RS485 过滤驱动**
   - UMDF 2.0 过滤驱动
   - 拦截和处理 RS485 协议
   - 提供增强的 RS485 功能
   - 注册为 FTDI 设备的上层过滤器

## 📁 **项目结构**

```
WDK_UMDF_RS485_Driver/
├── Driver/                     # UMDF 驱动源码
│   ├── RS485FilterDriver.cpp   # 主驱动实现
│   ├── RS485FilterDriver.h     # 驱动头文件
│   ├── RS485FilterDriver.inf   # 驱动安装文件
│   ├── DriverEntry.cpp         # 驱动入口点
│   ├── DllSupport.cpp         # DLL 导出
│   └── Build/                  # 构建输出
├── Installer/                  # 安装程序源码
│   ├── RS485DriverInstaller.cpp # 安装程序实现
│   ├── Resources.rc           # 嵌入资源
│   └── resource.h             # 资源定义
├── Scripts/                   # 构建脚本
│   └── BuildAndPackage.bat    # 自动化构建脚本
├── Package/                   # 最终输出
│   └── RS485DriverInstaller.exe # 最终安装程序
└── CDM2123620_Setup.exe      # FTDI 驱动 (需要下载)
```

## 🔧 **故障排除**

### 构建错误

1. **"找不到 WDK"**
   - 安装 Windows Driver Kit 10
   - 验证 WDK 安装路径

2. **"找不到 FTDI 驱动"**
   - 从 FTDI 下载 CDM2123620_Setup.exe
   - 放置在项目根目录

3. **"找不到 MSBuild"**
   - 安装 Visual Studio 2022
   - 使用开发者命令提示符

### 安装错误

1. **"访问被拒绝"**
   - 以管理员身份运行安装程序
   - 临时禁用杀毒软件

2. **"驱动未签名"**
   - 开发时使用测试签名模式
   - 生产环境获取正式代码签名证书

### 运行时问题

1. **设备无法识别**
   - 检查设备管理器
   - 验证 FTDI 驱动安装
   - 检查 USB 线缆和连接

2. **COM 端口不可用**
   - 安装后重启计算机
   - 检查设备管理器中的 COM 端口分配

## 🏗️ **开发说明**

### 驱动架构
- **UMDF 2.0** 框架
- **过滤驱动** 架构
- **FTDI 设备的上层过滤器**
- **用户模式协议处理**

### 安全性
- **安装需要管理员权限**
- **生产环境建议驱动签名**
- **自动处理 UAC 提升**

### 兼容性
- **Windows 10/11** x64
- **FTDI FT232** 系列设备
- **USB 2.0/3.0** 兼容

## 📞 **技术支持**

如需技术支持或有疑问：
1. 检查设备管理器中的驱动状态
2. 查看 Windows 事件日志
3. 启用驱动调试获取详细日志
4. 联系开发团队并提供具体错误信息

---

**🎉 完成后，您将获得一个单一的 .exe 文件，用户运行后即可自动安装完整的 RS485 驱动解决方案！**
