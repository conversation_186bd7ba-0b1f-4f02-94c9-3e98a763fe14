@echo off
REM RS485 Driver Build Script
REM Builds the complete RS485 driver solution using WDK build environment

echo ========================================
echo RS485 Driver Build Script
echo ========================================

REM Check if WDK build environment is available
if not defined BASEDIR (
    echo ERROR: WDK build environment not detected
    echo Please run this script from WDK Build Environment
    echo Start -^> Windows Kits -^> WDK -^> x64 Build Environment
    pause
    exit /b 1
)

echo WDK Build Environment: %BASEDIR%
echo Target Architecture: %_BUILDARCH%
echo Build Type: %DDKBUILDENV%

REM Set build directory
set BUILD_ROOT=%~dp0
echo Build Root: %BUILD_ROOT%

REM Create build output directories
if not exist "%BUILD_ROOT%Build" mkdir "%BUILD_ROOT%Build"
if not exist "%BUILD_ROOT%Build\Debug" mkdir "%BUILD_ROOT%Build\Debug"
if not exist "%BUILD_ROOT%Build\Release" mkdir "%BUILD_ROOT%Build\Release"

echo.
echo ========================================
echo Building Interface Library
echo ========================================
cd /d "%BUILD_ROOT%Interface"
build
if errorlevel 1 (
    echo ERROR: Interface library build failed
    pause
    exit /b 1
)
echo Interface library build completed successfully

echo.
echo ========================================
echo Building UMDF Driver
echo ========================================
cd /d "%BUILD_ROOT%Driver"
build
if errorlevel 1 (
    echo ERROR: Driver build failed
    pause
    exit /b 1
)
echo Driver build completed successfully

echo.
echo ========================================
echo Building Test Application
echo ========================================
cd /d "%BUILD_ROOT%Test"
build
if errorlevel 1 (
    echo ERROR: Test application build failed
    pause
    exit /b 1
)
echo Test application build completed successfully

echo.
echo ========================================
echo Copying Build Outputs
echo ========================================

REM Determine build output directory based on build type
if /i "%DDKBUILDENV%"=="chk" (
    set BUILD_TYPE=Debug
) else (
    set BUILD_TYPE=Release
)

set OUTPUT_DIR=%BUILD_ROOT%Build\%BUILD_TYPE%\%_BUILDARCH%
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Copy driver files
if exist "%BUILD_ROOT%Driver\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485FilterDriver.dll" (
    copy "%BUILD_ROOT%Driver\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485FilterDriver.dll" "%OUTPUT_DIR%\"
    echo Copied RS485FilterDriver.dll
)

if exist "%BUILD_ROOT%Driver\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485FilterDriver.pdb" (
    copy "%BUILD_ROOT%Driver\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485FilterDriver.pdb" "%OUTPUT_DIR%\"
    echo Copied RS485FilterDriver.pdb
)

REM Copy INF file
if exist "%BUILD_ROOT%Driver\RS485FilterDriver.inf" (
    copy "%BUILD_ROOT%Driver\RS485FilterDriver.inf" "%OUTPUT_DIR%\"
    echo Copied RS485FilterDriver.inf
)

REM Copy interface library
if exist "%BUILD_ROOT%Interface\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485DriverInterface.lib" (
    copy "%BUILD_ROOT%Interface\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485DriverInterface.lib" "%OUTPUT_DIR%\"
    echo Copied RS485DriverInterface.lib
)

REM Copy test application
if exist "%BUILD_ROOT%Test\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485Test.exe" (
    copy "%BUILD_ROOT%Test\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485Test.exe" "%OUTPUT_DIR%\"
    echo Copied RS485Test.exe
)

if exist "%BUILD_ROOT%Test\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485Test.pdb" (
    copy "%BUILD_ROOT%Test\obj%BUILD_FLAVOR%\%_BUILDARCH%\RS485Test.pdb" "%OUTPUT_DIR%\"
    echo Copied RS485Test.pdb
)

echo.
echo ========================================
echo Build Summary
echo ========================================
echo Build Type: %BUILD_TYPE%
echo Architecture: %_BUILDARCH%
echo Output Directory: %OUTPUT_DIR%
echo.
echo Build completed successfully!
echo.
echo Next Steps:
echo 1. Enable test signing: bcdedit /set testsigning on
echo 2. Install driver: Right-click RS485FilterDriver.inf -^> Install
echo 3. Run test: %OUTPUT_DIR%\RS485Test.exe
echo.

cd /d "%BUILD_ROOT%"
pause
