@echo off
echo Building Fixed RS485 Test UI...

REM Try to find Visual Studio
set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
if exist "%VSPATH%" (
    call "%VSPATH%" -arch=x64 >nul 2>&1
) else (
    echo Visual Studio 2022 not found, trying alternative paths...
    set "VSPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
    if exist "%VSPATH%" (
        call "%VSPATH%" -arch=x64 >nul 2>&1
    )
)

echo Compiling RS485TestUI_Fixed.exe...

cl.exe /EHsc /std:c++17 /Fe:RS485TestUI_Fixed.exe /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE RS485TestUI.cpp /link user32.lib gdi32.lib comctl32.lib setupapi.lib /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo SUCCESS: RS485TestUI_Fixed.exe compiled successfully!
    echo.
    echo Fixed Issues:
    echo - Removed all Chinese characters
    echo - Fixed UI responsiveness
    echo - Added proper message processing
    echo - Improved error handling
    echo.
    echo Run: .\RS485TestUI_Fixed.exe
) else (
    echo.
    echo FAILED: Compilation error occurred
)

pause
