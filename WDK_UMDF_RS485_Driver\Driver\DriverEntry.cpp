//
// RS485 Filter Driver - Driver Entry Point
// Copyright (c) AI-SLDAP. All rights reserved.
//

#include "RS485FilterDriver.h"

//
// Driver Entry Point - Required for UMDF drivers
//
extern "C" NTSTATUS DriverEntry(
    _In_ PDRIVER_OBJECT DriverObject,
    _In_ PUNICODE_STRING RegistryPath
)
{
    NTSTATUS status;
    WDF_DRIVER_CONFIG config;
    WDF_OBJECT_ATTRIBUTES attributes;

    RS485_DEBUG_PRINT("RS485 Filter Driver - DriverEntry called");

    // Initialize WDF driver configuration
    WDF_DRIVER_CONFIG_INIT(&config, RS485EvtDeviceAdd);
    config.DriverInitFlags = WdfDriverInitNonPnpDriver;

    // Initialize object attributes
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    attributes.EvtCleanupCallback = RS485EvtDriverContextCleanup;

    // Create the driver object
    status = WdfDriverCreate(
        DriverObject,
        RegistryPath,
        &attributes,
        &config,
        WDF_NO_HANDLE
    );

    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("WdfDriverCreate failed: 0x%x", status);
        return status;
    }

    RS485_DEBUG_PRINT("RS485 Filter Driver - DriverEntry completed successfully");
    return status;
}
