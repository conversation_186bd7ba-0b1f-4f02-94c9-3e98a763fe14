{"Version": 1, "WorkspaceRootPath": "D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{12345678-1234-1234-1234-123456789ABC}|Driver\\RS485FilterDriver.vcxproj|D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Driver\\RS485FilterDriver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{12345678-1234-1234-1234-123456789ABC}|Driver\\RS485FilterDriver.vcxproj|solutionrelative:Driver\\RS485FilterDriver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{12345678-1234-1234-1234-123456789ABC}|Driver\\RS485FilterDriver.vcxproj|D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Include\\RS485Common.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{12345678-1234-1234-1234-123456789ABC}|Driver\\RS485FilterDriver.vcxproj|solutionrelative:Include\\RS485Common.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Msbuild\\Microsoft\\VC\\v170\\Microsoft.CppBuild.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "RS485FilterDriver.h", "DocumentMoniker": "D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Driver\\RS485FilterDriver.h", "RelativeDocumentMoniker": "Driver\\RS485FilterDriver.h", "ToolTip": "D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Driver\\RS485FilterDriver.h", "RelativeToolTip": "Driver\\RS485FilterDriver.h", "ViewState": "AgIAAP4AAAAAAAAAAAAAABABAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-17T07:27:38.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RS485Common.h", "DocumentMoniker": "D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Include\\RS485Common.h", "RelativeDocumentMoniker": "Include\\RS485Common.h", "ToolTip": "D:\\wjw_new_file\\Software_design\\RS485_Driver\\RS485_driver_development\\WDK_UMDF_RS485_Driver\\Include\\RS485Common.h", "RelativeToolTip": "Include\\RS485Common.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-17T07:26:20.242Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Microsoft.CppBuild.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Msbuild\\Microsoft\\VC\\v170\\Microsoft.CppBuild.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Msbuild\\Microsoft\\VC\\v170\\Microsoft.CppBuild.targets", "ViewState": "AgIAALUBAAAAAAAAAAAAANABAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-07-17T06:53:46.008Z"}]}]}]}