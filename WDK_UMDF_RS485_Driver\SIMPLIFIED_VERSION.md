# RS485驱动程序 - 简化版本指南

## 构建挑战分析

我们创建的RS485驱动程序项目是基于Windows Driver Kit (WDK)和User-Mode Driver Framework (UMDF)的完整驱动程序实现。这需要安装特定的开发环境和工具链。

由于您可能没有安装完整的WDK环境，我们提供以下替代方案：

## 替代方案

### 方案1：安装完整WDK环境

按照`WDK_INSTALLATION_GUIDE.md`中的说明安装Windows Driver Kit和相关组件。这是开发完整UMDF驱动程序的推荐方法。

### 方案2：创建简化版本应用程序

如果不想安装完整WDK，可以创建一个简化版本的RS485通信应用程序，使用标准Windows API而不是UMDF框架。

## 简化版本实现

### 简化版本架构

简化版本将：
1. 使用Windows API直接与COM端口通信
2. 实现相同的ZES协议帧格式
3. 提供相同的高级API接口
4. 省略驱动程序特定的功能

### 创建简化版本的步骤

1. **创建新文件夹**：
   ```
   d:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485_Simplified
   ```

2. **复制以下文件**：
   - `Include/RS485Protocol.h` (协议定义)
   - `Include/RS485Errors.h` (错误代码)
   - `Interface/RS485DriverInterface.h` (API接口)
   - `Interface/RS485DriverInterface.cpp` (API实现)
   - `Test/RS485Test.cpp` (测试程序)

3. **修改头文件**：
   - 移除WDK特定的包含和定义
   - 替换为标准Windows API

4. **创建简化版本的实现文件**：
   - 使用CreateFile/ReadFile/WriteFile替代DeviceIoControl
   - 使用标准COM端口通信

## 简化版本示例代码

### 简化版本的RS485Common.h

```cpp
#pragma once

// 标准Windows头文件
#include <windows.h>
#include <string>
#include <vector>
#include <functional>
#include <mutex>
#include <memory>

// 协议常量
#define RS485_FRAME_SIZE            16      // 完整帧大小
#define RS485_PAYLOAD_SIZE          12      // 核心载荷大小
#define RS485_HEADER_BYTE           0xAA    // 帧起始标记
#define RS485_TRAILER_BYTE          0x0D    // 帧结束标记

// 缓冲区配置
#define RS485_UPLINK_BUFFER_SLOTS   5       // PC到设备缓冲区容量
#define RS485_DOWNLINK_BUFFER_SLOTS 10      // 设备到PC缓冲区容量

// 功能代码
#define RS485_FUNCTION_ASSIGN_DATA      0b111   // 主机：分配数据
#define RS485_FUNCTION_REQUEST_DATA     0b110   // 主机：请求数据
#define RS485_FUNCTION_RESPONSE_ASSIGN  0b010   // 从机：分配响应
#define RS485_FUNCTION_RESPONSE_REQUEST 0b001   // 从机：请求响应
#define RS485_FUNCTION_RESEND_REQUEST   0b000   // 重发请求

// 设备地址常量
#define RS485_BROADCAST_ADDRESS     0x00    // 广播地址
#define RS485_MIN_SLAVE_ADDRESS     0x01    // 最小有效从机地址
#define RS485_MAX_SLAVE_ADDRESS     0x1F    // 最大有效从机地址

// 超时和重试配置
#define RS485_RESPONSE_TIMEOUT_MS   100     // 响应窗口时间
#define RS485_MAX_RETRY_COUNT       3       // 最大重试次数
#define RS485_RETRY_DELAY_MS        50      // 重试间隔

// 设备接口GUID
// {12345678-1234-1234-1234-123456789ABC}
DEFINE_GUID(GUID_DEVINTERFACE_RS485_FILTER,
    0x12345678, 0x1234, 0x1234, 0x12, 0x34, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC);

// 实用宏
#define RS485_EXTRACT_FUNCTION_CODE(idByte)     (((idByte) >> 5) & 0x07)
#define RS485_EXTRACT_DEVICE_ADDRESS(idByte)    ((idByte) & 0x1F)
#define RS485_BUILD_ID_BYTE(funcCode, address)  (((funcCode) << 5) | ((address) & 0x1F))

#define RS485_IS_VALID_SLAVE_ADDRESS(addr)      ((addr) >= RS485_MIN_SLAVE_ADDRESS && \
                                                 (addr) <= RS485_MAX_SLAVE_ADDRESS)

#define RS485_IS_BROADCAST_ADDRESS(addr)        ((addr) == RS485_BROADCAST_ADDRESS)
```

### 简化版本的COM端口通信

```cpp
// 简化版本的COM端口通信类
class RS485ComPort {
public:
    RS485ComPort() : m_hComPort(INVALID_HANDLE_VALUE), m_isOpen(false) {}
    
    ~RS485ComPort() {
        Close();
    }
    
    bool Open(const std::string& portName) {
        // 格式化COM端口名称
        std::string fullPortName = "\\\\.\\" + portName;
        
        // 打开COM端口
        m_hComPort = CreateFileA(
            fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            NULL,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            NULL
        );
        
        if (m_hComPort == INVALID_HANDLE_VALUE) {
            return false;
        }
        
        // 配置COM端口
        DCB dcb = {0};
        dcb.DCBlength = sizeof(DCB);
        
        if (!GetCommState(m_hComPort, &dcb)) {
            Close();
            return false;
        }
        
        // 设置波特率和其他参数
        dcb.BaudRate = 9600;
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;
        
        if (!SetCommState(m_hComPort, &dcb)) {
            Close();
            return false;
        }
        
        // 设置超时
        COMMTIMEOUTS timeouts = {0};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 50;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 50;
        timeouts.WriteTotalTimeoutMultiplier = 10;
        
        if (!SetCommTimeouts(m_hComPort, &timeouts)) {
            Close();
            return false;
        }
        
        m_isOpen = true;
        return true;
    }
    
    void Close() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }
        m_isOpen = false;
    }
    
    bool IsOpen() const {
        return m_isOpen;
    }
    
    bool Write(const void* data, DWORD size) {
        if (!m_isOpen || m_hComPort == INVALID_HANDLE_VALUE) {
            return false;
        }
        
        DWORD bytesWritten = 0;
        if (!WriteFile(m_hComPort, data, size, &bytesWritten, NULL)) {
            return false;
        }
        
        return (bytesWritten == size);
    }
    
    bool Read(void* buffer, DWORD size, DWORD* bytesRead) {
        if (!m_isOpen || m_hComPort == INVALID_HANDLE_VALUE) {
            return false;
        }
        
        return ReadFile(m_hComPort, buffer, size, bytesRead, NULL) != 0;
    }
    
private:
    HANDLE m_hComPort;
    bool m_isOpen;
};
```

## 构建简化版本

### 使用Visual Studio构建

1. 创建新的Visual Studio项目：
   - 文件 → 新建 → 项目
   - 选择"控制台应用程序"
   - 命名为"RS485_Simplified"

2. 添加源文件：
   - 添加修改后的头文件
   - 添加简化版本的实现文件
   - 添加测试程序

3. 配置项目属性：
   - 字符集：使用Unicode字符集
   - C++标准：C++17或更高
   - 包含目录：添加必要的包含目录

4. 构建解决方案

### 使用命令行构建

```cmd
cd /d "d:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485_Simplified"

cl /EHsc /std:c++17 /W4 /DUNICODE /D_UNICODE RS485Test.cpp RS485DriverInterface.cpp /link setupapi.lib
```

## 测试简化版本

1. 连接RS485硬件
2. 运行测试程序：
   ```
   RS485Test.exe
   ```

3. 验证基本功能：
   - 设备枚举
   - 连接测试
   - 配置命令
   - 数据请求

## 简化版本的局限性

简化版本有以下局限性：

1. **不是真正的驱动程序**：
   - 无法提供驱动程序级别的功能
   - 无法与设备堆栈集成

2. **功能受限**：
   - 无法处理多设备并发
   - 无法提供内核级别的性能
   - 无法提供驱动程序级别的安全性

3. **兼容性问题**：
   - 可能与某些硬件不兼容
   - 可能需要特定的COM端口设置

## 结论

简化版本提供了一种不需要WDK的替代方案，可以实现基本的RS485通信功能。如果需要完整的驱动程序功能，建议安装WDK并使用原始项目。

如果您决定继续使用简化版本，我们可以提供更详细的实现指导。
