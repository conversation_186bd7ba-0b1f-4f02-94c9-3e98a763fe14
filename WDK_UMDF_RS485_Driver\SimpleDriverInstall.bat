@echo off
echo ===================================================================
echo RS485 UMDF Driver - Simple Installation Solution
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Creating certificate...
echo ===================================================================

REM Create certificate using simple PowerShell command
powershell -Command "New-SelfSignedCertificate -Subject 'CN=RS485DriverCert' -Type CodeSigningCert -CertStoreLocation 'Cert:\LocalMachine\My'"

if %errorLevel% neq 0 (
    echo WARNING: Certificate creation failed, but continuing...
)

echo.
echo Step 2: Installing certificate to trusted stores...
echo ===================================================================

REM Export and install certificate
powershell -Command "Get-ChildItem -Path Cert:\LocalMachine\My | Where-Object {$_.Subject -like '*RS485DriverCert*'} | Export-Certificate -FilePath 'RS485Cert.cer' -Force"
powershell -Command "Import-Certificate -FilePath 'RS485Cert.cer' -CertStoreLocation 'Cert:\LocalMachine\Root'"
powershell -Command "Import-Certificate -FilePath 'RS485Cert.cer' -CertStoreLocation 'Cert:\LocalMachine\TrustedPublisher'"

echo Certificate installed to trusted stores.
echo.

echo Step 3: Building driver...
echo ===================================================================

REM Build the driver
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" Driver\RS485FilterDriver.vcxproj /p:Configuration=Release /p:Platform=x64

if %errorLevel% neq 0 (
    echo ERROR: Driver build failed!
    pause
    exit /b 1
)

echo Driver built successfully.
echo.

echo Step 4: Signing driver files...
echo ===================================================================

REM Sign the driver DLL
signtool sign /v /s My /n "RS485DriverCert" /fd SHA256 "Driver\Build\Release\x64\RS485FilterDriver.dll"

if %errorLevel% equ 0 (
    echo Driver DLL signed successfully.
) else (
    echo WARNING: Driver DLL signing failed, but continuing...
)

echo.
echo Step 5: Building installer...
echo ===================================================================

REM Build the installer
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" Installer\RS485DriverInstaller.vcxproj /p:Configuration=Release /p:Platform=x64

if %errorLevel% neq 0 (
    echo ERROR: Installer build failed!
    pause
    exit /b 1
)

REM Copy to final output
copy "Installer\Build\Release\x64\RS485DriverInstaller.exe" "FinalOutput\" /Y

echo Installer built successfully.
echo.

echo Step 6: Installing the driver...
echo ===================================================================

echo Running the installer...
.\FinalOutput\RS485DriverInstaller.exe

echo.
echo ===================================================================
echo Installation Complete!
echo ===================================================================
echo.
echo Your RS485 UMDF driver has been installed.
echo.
echo To verify:
echo 1. Connect your FTDI device
echo 2. Check Device Manager under "Ports (COM & LPT)"
echo 3. Look for the RS485 filter driver
echo.

pause
