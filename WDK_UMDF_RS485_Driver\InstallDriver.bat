@echo off
REM ===================================================================
REM RS485 Driver One-Click Installation Script
REM ===================================================================

echo ===================================================================
echo RS485 Driver Installation Helper
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Check test signing status
echo Checking test signing status...
bcdedit /enum {current} | findstr /i testsigning > testsigning.txt
set /p TESTSIGNING=<testsigning.txt
del testsigning.txt

echo.
if not "%TESTSIGNING%"=="" (
    echo Current test signing status: %TESTSIGNING%
) else (
    echo Test signing is DISABLED
)
echo.

echo Choose installation method:
echo ===================================================================
echo 1. Standard Installation (Recommended for production)
echo 2. Development Installation with Test Signing
echo 3. Quick Development Installation (Alternative method)
echo 4. Exit
echo.

set /p CHOICE=Enter your choice (1-4): 

if "%CHOICE%"=="1" goto STANDARD_INSTALL
if "%CHOICE%"=="2" goto DEV_INSTALL
if "%CHOICE%"=="3" goto QUICK_INSTALL
if "%CHOICE%"=="4" goto END

echo Invalid choice. Please try again.
goto END

:STANDARD_INSTALL
echo.
echo ===================================================================
echo Standard Installation
echo ===================================================================
echo.
echo This will install the RS485 driver using the standard method.
echo If the driver is properly signed, this should work on all systems.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Running installer...
.\FinalOutput\RS485DriverInstaller.exe
goto END

:DEV_INSTALL
echo.
echo ===================================================================
echo Development Installation with Test Signing
echo ===================================================================
echo.
echo This will:
echo 1. Enable test signing mode
echo 2. Create and install a test certificate
echo 3. Sign the driver files
echo 4. Install the driver
echo.
echo NOTE: Your computer will need to restart during this process.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Step 1: Enabling test signing and preparing certificate...
echo.
call FixedSignDriver.bat

echo.
echo After reboot, run this script again and select option 1.
goto END

:QUICK_INSTALL
echo.
echo ===================================================================
echo Quick Development Installation (Alternative method)
echo ===================================================================
echo.
echo This will install the driver using the alternative method,
echo which bypasses the normal driver signing requirements.
echo.
echo NOTE: This is for DEVELOPMENT ONLY and not recommended for production.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Running installer with alternative method...
.\FinalOutput\RS485DriverInstaller.exe
goto END

:END
echo.
echo ===================================================================
echo Installation process completed.
echo.
echo If you encountered any issues, please refer to:
echo INSTALLATION_GUIDE.md for detailed troubleshooting.
echo.
pause
