//
// RS485 Driver Test Program
// Tests the UMDF RS485 filter driver functionality
//

#include <windows.h>
#include <iostream>
#include <string>
#include <vector>
#include <setupapi.h>
#include <devguid.h>

#pragma comment(lib, "setupapi.lib")

class RS485DriverTester {
private:
    HANDLE m_deviceHandle;
    std::string m_devicePath;

public:
    RS485DriverTester() : m_deviceHandle(INVALID_HANDLE_VALUE) {}
    
    ~RS485DriverTester() {
        if (m_deviceHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(m_deviceHandle);
        }
    }

    bool FindRS485Device() {
        std::cout << "Searching for RS485/FTDI devices..." << std::endl;
        
        // Get device information set for all COM ports
        HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
            &GUID_DEVCLASS_PORTS,
            nullptr,
            nullptr,
            DIGCF_PRESENT
        );

        if (deviceInfoSet == INVALID_HANDLE_VALUE) {
            std::cout << "Failed to get device information set." << std::endl;
            return false;
        }

        SP_DEVINFO_DATA deviceInfoData;
        deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
        
        for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
            char deviceDesc[256];
            char hardwareId[256];
            
            // Get device description
            if (SetupDiGetDeviceRegistryPropertyA(
                deviceInfoSet, &deviceInfoData, SPDRP_DEVICEDESC,
                nullptr, (PBYTE)deviceDesc, sizeof(deviceDesc), nullptr)) {
                
                // Get hardware ID
                if (SetupDiGetDeviceRegistryPropertyA(
                    deviceInfoSet, &deviceInfoData, SPDRP_HARDWAREID,
                    nullptr, (PBYTE)hardwareId, sizeof(hardwareId), nullptr)) {
                    
                    std::cout << "Found device: " << deviceDesc << std::endl;
                    std::cout << "Hardware ID: " << hardwareId << std::endl;
                    
                    // Check if it's an FTDI device
                    if (strstr(hardwareId, "VID_0403") != nullptr) {
                        std::cout << "✅ FTDI device detected!" << std::endl;
                        
                        // Get COM port name
                        HKEY hKey;
                        if (SetupDiOpenDevRegKey(deviceInfoSet, &deviceInfoData, 
                            DICS_FLAG_GLOBAL, 0, DIREG_DEV, KEY_READ) != INVALID_HANDLE_VALUE) {
                            
                            char portName[256];
                            DWORD portNameSize = sizeof(portName);
                            DWORD type;
                            
                            hKey = SetupDiOpenDevRegKey(deviceInfoSet, &deviceInfoData, 
                                DICS_FLAG_GLOBAL, 0, DIREG_DEV, KEY_READ);
                            
                            if (RegQueryValueExA(hKey, "PortName", nullptr, &type, 
                                (LPBYTE)portName, &portNameSize) == ERROR_SUCCESS) {
                                std::cout << "COM Port: " << portName << std::endl;
                                m_devicePath = std::string("\\\\.\\") + portName;
                                RegCloseKey(hKey);
                                SetupDiDestroyDeviceInfoList(deviceInfoSet);
                                return true;
                            }
                            RegCloseKey(hKey);
                        }
                    }
                }
            }
            std::cout << "---" << std::endl;
        }

        SetupDiDestroyDeviceInfoList(deviceInfoSet);
        return false;
    }

    bool OpenDevice() {
        if (m_devicePath.empty()) {
            std::cout << "No device path available. Run FindRS485Device() first." << std::endl;
            return false;
        }

        std::cout << "Opening device: " << m_devicePath << std::endl;
        
        m_deviceHandle = CreateFileA(
            m_devicePath.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (m_deviceHandle == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            std::cout << "Failed to open device. Error: " << error << std::endl;
            return false;
        }

        std::cout << "✅ Device opened successfully!" << std::endl;
        return true;
    }

    bool TestBasicCommunication() {
        if (m_deviceHandle == INVALID_HANDLE_VALUE) {
            std::cout << "Device not opened." << std::endl;
            return false;
        }

        std::cout << "Testing basic communication..." << std::endl;

        // Configure COM port settings
        DCB dcb;
        if (!GetCommState(m_deviceHandle, &dcb)) {
            std::cout << "Failed to get COM state." << std::endl;
            return false;
        }

        dcb.BaudRate = CBR_9600;
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;

        if (!SetCommState(m_deviceHandle, &dcb)) {
            std::cout << "Failed to set COM state." << std::endl;
            return false;
        }

        // Set timeouts
        COMMTIMEOUTS timeouts;
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 1000;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 1000;
        timeouts.WriteTotalTimeoutMultiplier = 10;

        if (!SetCommTimeouts(m_deviceHandle, &timeouts)) {
            std::cout << "Failed to set timeouts." << std::endl;
            return false;
        }

        std::cout << "✅ COM port configured successfully!" << std::endl;
        return true;
    }

    bool TestRS485Protocol() {
        if (m_deviceHandle == INVALID_HANDLE_VALUE) {
            std::cout << "Device not opened." << std::endl;
            return false;
        }

        std::cout << "Testing RS485 protocol..." << std::endl;

        // Test RS485 frame: [Header][ID][Payload][CRC][Trailer]
        // Example: 0xAA 0x01 [12-byte payload] 0xXX 0x0D
        uint8_t testFrame[] = {
            0xAA,                           // Header
            0x01,                           // ID
            'T', 'E', 'S', 'T',            // Payload (12 bytes)
            0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00,
            0x00,                           // CRC (placeholder)
            0x0D                            // Trailer
        };

        DWORD bytesWritten;
        if (!WriteFile(m_deviceHandle, testFrame, sizeof(testFrame), &bytesWritten, nullptr)) {
            std::cout << "Failed to write test frame." << std::endl;
            return false;
        }

        std::cout << "✅ Test frame sent (" << bytesWritten << " bytes)" << std::endl;

        // Try to read response
        uint8_t responseBuffer[256];
        DWORD bytesRead;
        
        if (ReadFile(m_deviceHandle, responseBuffer, sizeof(responseBuffer), &bytesRead, nullptr)) {
            if (bytesRead > 0) {
                std::cout << "✅ Received response (" << bytesRead << " bytes): ";
                for (DWORD i = 0; i < bytesRead && i < 16; i++) {
                    printf("%02X ", responseBuffer[i]);
                }
                std::cout << std::endl;
            } else {
                std::cout << "No response received (this may be normal if no device is connected)." << std::endl;
            }
        }

        return true;
    }

    void RunFullTest() {
        std::cout << "====================================================================" << std::endl;
        std::cout << "RS485 UMDF Driver Test Program" << std::endl;
        std::cout << "====================================================================" << std::endl;
        std::cout << std::endl;

        // Step 1: Find device
        if (!FindRS485Device()) {
            std::cout << "❌ No FTDI/RS485 device found." << std::endl;
            std::cout << "Please ensure:" << std::endl;
            std::cout << "1. FTDI device is connected" << std::endl;
            std::cout << "2. FTDI VCP driver is installed" << std::endl;
            std::cout << "3. RS485 filter driver is installed" << std::endl;
            return;
        }

        // Step 2: Open device
        if (!OpenDevice()) {
            std::cout << "❌ Failed to open device." << std::endl;
            return;
        }

        // Step 3: Test basic communication
        if (!TestBasicCommunication()) {
            std::cout << "❌ Basic communication test failed." << std::endl;
            return;
        }

        // Step 4: Test RS485 protocol
        if (!TestRS485Protocol()) {
            std::cout << "❌ RS485 protocol test failed." << std::endl;
            return;
        }

        std::cout << std::endl;
        std::cout << "====================================================================" << std::endl;
        std::cout << "✅ RS485 Driver Test Completed Successfully!" << std::endl;
        std::cout << "====================================================================" << std::endl;
        std::cout << std::endl;
        std::cout << "Your RS485 UMDF driver is working correctly." << std::endl;
        std::cout << "You can now develop applications using this driver." << std::endl;
    }
};

int main() {
    RS485DriverTester tester;
    tester.RunFullTest();
    
    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
