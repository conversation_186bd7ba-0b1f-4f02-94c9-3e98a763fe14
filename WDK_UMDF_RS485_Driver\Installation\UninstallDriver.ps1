#
# AI-SLDAP RS485 Driver Uninstallation Script (PowerShell)
# Removes RS485 Filter Driver (FTDI VCP driver is left intact)
#
# This script must be run as Administrator

param(
    [switch]$Silent = $false,
    [switch]$RemoveFTDI = $false
)

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    if (-not $Silent) { Read-Host "Press Enter to exit" }
    exit 1
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI-SLDAP RS485 Driver Uninstallation" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to remove RS485 driver
function Remove-RS485Driver {
    Write-Host "Removing AI-SLDAP RS485 Filter Driver..." -ForegroundColor Yellow
    
    try {
        # Find and remove our driver from the driver store
        $drivers = & pnputil /enum-drivers | Select-String -Pattern "RS485" -Context 2,0
        
        if ($drivers) {
            Write-Host "Found RS485 drivers in driver store:" -ForegroundColor Green
            
            # Extract OEM*.inf filenames
            $oemFiles = @()
            foreach ($line in $drivers) {
                if ($line -match "oem\d+\.inf") {
                    $oemFiles += $matches[0]
                }
            }
            
            # Remove each OEM file
            foreach ($oemFile in $oemFiles) {
                Write-Host "Removing driver package: $oemFile" -ForegroundColor Yellow
                
                $result = & pnputil /delete-driver $oemFile /uninstall /force 2>&1
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Successfully removed: $oemFile" -ForegroundColor Green
                } else {
                    Write-Host "WARNING: Failed to remove $oemFile" -ForegroundColor Yellow
                    Write-Host "Output: $result" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "No RS485 drivers found in driver store." -ForegroundColor Yellow
        }
        
        # Try to remove driver files from system32
        $driverPath = "$env:SystemRoot\System32\drivers\UMDF\RS485FilterDriver.dll"
        if (Test-Path $driverPath) {
            try {
                Remove-Item $driverPath -Force
                Write-Host "Removed driver file: $driverPath" -ForegroundColor Green
            } catch {
                Write-Host "WARNING: Could not remove driver file: $driverPath" -ForegroundColor Yellow
                Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Gray
            }
        }
        
    } catch {
        Write-Host "ERROR: Failed to remove RS485 driver: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Function to remove FTDI driver (optional)
function Remove-FTDIDriver {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Removing FTDI VCP Driver" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "WARNING: This will remove FTDI VCP drivers from your system!" -ForegroundColor Red
    Write-Host "This may affect other FTDI devices you have connected." -ForegroundColor Yellow
    Write-Host ""
    
    if (-not $Silent) {
        $confirm = Read-Host "Are you sure you want to remove FTDI drivers? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Host "FTDI driver removal cancelled." -ForegroundColor Yellow
            return $true
        }
    }
    
    try {
        # Find FTDI drivers
        $ftdiDrivers = & pnputil /enum-drivers | Select-String -Pattern "FTDI" -Context 2,0
        
        if ($ftdiDrivers) {
            Write-Host "Found FTDI drivers in driver store:" -ForegroundColor Green
            
            # Extract OEM*.inf filenames
            $oemFiles = @()
            foreach ($line in $ftdiDrivers) {
                if ($line -match "oem\d+\.inf") {
                    $oemFiles += $matches[0]
                }
            }
            
            # Remove each OEM file
            foreach ($oemFile in $oemFiles) {
                Write-Host "Removing FTDI driver package: $oemFile" -ForegroundColor Yellow
                
                $result = & pnputil /delete-driver $oemFile /uninstall /force 2>&1
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Successfully removed: $oemFile" -ForegroundColor Green
                } else {
                    Write-Host "WARNING: Failed to remove $oemFile" -ForegroundColor Yellow
                    Write-Host "Output: $result" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "No FTDI drivers found in driver store." -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "ERROR: Failed to remove FTDI driver: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Function to verify removal
function Test-Removal {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Verifying Removal" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "Checking for remaining RS485 drivers..." -ForegroundColor Yellow
    
    try {
        $drivers = & pnputil /enum-drivers | Select-String -Pattern "RS485"
        
        if ($drivers) {
            Write-Host "WARNING: Some RS485 drivers may still be present:" -ForegroundColor Yellow
            $drivers | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        } else {
            Write-Host "No RS485 drivers found in driver store." -ForegroundColor Green
        }
        
    } catch {
        Write-Host "WARNING: Could not verify removal: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Main uninstallation process
try {
    Write-Host "Starting RS485 driver removal..." -ForegroundColor Yellow
    Write-Host ""
    
    # Remove RS485 driver
    if (-not (Remove-RS485Driver)) {
        Write-Host "RS485 driver removal failed!" -ForegroundColor Red
        if (-not $Silent) { Read-Host "Press Enter to exit" }
        exit 1
    }
    
    # Remove FTDI driver if requested
    if ($RemoveFTDI) {
        if (-not (Remove-FTDIDriver)) {
            Write-Host "FTDI driver removal failed!" -ForegroundColor Red
        }
    } else {
        Write-Host "FTDI VCP driver left intact (use -RemoveFTDI to remove it)." -ForegroundColor Green
    }
    
    Write-Host ""
    
    # Verify removal
    Test-Removal
    
    # Success message
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Uninstallation Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "The AI-SLDAP RS485 Driver has been successfully removed." -ForegroundColor Green
    Write-Host ""
    Write-Host "Note:" -ForegroundColor Cyan
    Write-Host "- A system reboot may be required to complete the removal" -ForegroundColor White
    Write-Host "- FTDI VCP drivers were left intact unless -RemoveFTDI was used" -ForegroundColor White
    Write-Host "- Check Device Manager to verify all devices are working correctly" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "FATAL ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if (-not $Silent) { Read-Host "Press Enter to exit" }
    exit 1
}

if (-not $Silent) {
    Read-Host "Press Enter to exit"
}

Write-Host "Uninstallation script completed." -ForegroundColor Green
