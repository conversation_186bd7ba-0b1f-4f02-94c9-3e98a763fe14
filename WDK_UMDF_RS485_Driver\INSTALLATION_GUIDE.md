# RS485 驱动安装指南

## 目录
1. [开发环境安装](#开发环境安装)
2. [生产环境安装](#生产环境安装)
3. [故障排除](#故障排除)
4. [常见问题](#常见问题)

## 开发环境安装

### 方法 1: 启用测试签名（推荐）

这是开发和测试环境的推荐方法，它允许安装未签名或使用测试证书签名的驱动程序。

1. **启用测试签名模式**：
   ```batch
   # 以管理员身份运行命令提示符
   bcdedit /set testsigning on
   
   # 重启计算机
   shutdown /r /t 0
   ```

2. **运行签名脚本**：
   ```batch
   # 以管理员身份运行
   .\SignDriver.bat
   ```
   此脚本将：
   - 创建目录文件 (.cat)
   - 创建测试证书
   - 签名驱动文件
   - 将证书安装到受信任的根
   - 启用测试签名模式

3. **重启后运行安装程序**：
   ```batch
   .\FinalOutput\RS485DriverInstaller.exe
   ```

### 方法 2: 使用备用安装方法

如果您不想启用测试签名，安装程序会自动使用备用方法：

1. **直接运行安装程序**：
   ```batch
   .\FinalOutput\RS485DriverInstaller.exe
   ```

2. **当看到以下消息时，安装已成功**：
   ```
   Using alternative installation method for development...
   Driver DLL copied to System32 successfully.
   Alternative installation completed.
   ```

## 生产环境安装

对于生产环境，您需要正确签名驱动程序：

1. **获取代码签名证书**：
   - 从受信任的证书颁发机构购买代码签名证书
   - 或使用您组织的内部证书

2. **创建目录文件并签名**：
   ```batch
   # 创建目录文件
   inf2cat /driver:Driver /os:10_X64
   
   # 使用您的证书签名
   signtool sign /v /s "YourCertStore" /n "YourCertName" /t http://timestamp.digicert.com Driver\RS485FilterDriver.cat
   signtool sign /v /s "YourCertStore" /n "YourCertName" /t http://timestamp.digicert.com Driver\Build\Release\x64\RS485FilterDriver.dll
   ```

3. **重新构建安装程序**：
   ```batch
   .\BuildAll.bat
   ```

4. **分发安装程序**：
   - 最终用户不需要启用测试签名
   - 安装程序将使用标准安装方法

## 故障排除

### 错误: 0xE000022F (3758096943)

**症状**：
```
Initial driver installation failed. Error: 3758096943
ERROR: Catalog file missing or security validation failed
```

**解决方案**：
1. 确保已启用测试签名模式：
   ```batch
   bcdedit /set testsigning on
   ```
   然后重启计算机。

2. 或者使用备用安装方法（安装程序会自动尝试）。

### 错误: 驱动程序未签名

**症状**：
```
Windows 无法验证此驱动程序的发布者
```

**解决方案**：
1. 对于开发：运行 `SignDriver.bat` 创建测试证书并签名
2. 对于生产：使用有效的代码签名证书签名

### 错误: 设备未出现

**症状**：
安装成功但设备管理器中未显示设备

**解决方案**：
1. 确保 FTDI 设备已连接
2. 检查设备管理器中的"其他设备"部分是否有未识别设备
3. 尝试重新插拔设备

## 常见问题

### 我需要为每台计算机启用测试签名吗？

- **开发环境**：是的，每台用于开发或测试的计算机都需要启用测试签名
- **生产环境**：不需要，如果驱动程序使用有效的代码签名证书签名

### 如何检查测试签名是否已启用？

```batch
bcdedit /enum {current} | findstr /i testsigning
```

如果启用，将显示 `testsigning Yes`。

### 备用安装方法是否安全用于生产？

不推荐。备用方法是为开发和测试环境设计的。对于生产环境，应该：
1. 使用有效的代码签名证书签名驱动程序
2. 使用标准安装方法

### 如何卸载驱动程序？

```batch
# 以管理员身份运行
pnputil /enum-drivers
pnputil /delete-driver oem##.inf /force  # 替换 oem##.inf 为实际的 OEM INF 文件
```

### 如何验证驱动程序是否正确安装？

1. 打开设备管理器
2. 检查"端口 (COM & LPT)"下是否有 FTDI 设备
3. 右键点击设备并选择"属性"
4. 在"驱动程序"选项卡中，应该显示 RS485 驱动程序
