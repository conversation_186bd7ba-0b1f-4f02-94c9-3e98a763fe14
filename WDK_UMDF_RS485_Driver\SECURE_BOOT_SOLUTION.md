# 🔐 Secure Boot 环境下的 RS485 UMDF 驱动解决方案

## 🎯 **项目目标确认**

我们的目标是使用 **Windows Driver Kit (WDK)** 和 **User-Mode Driver Framework (UMDF)** 构建一个完整的 RS485 驱动程序，该驱动程序：

1. **基于 UMDF 2.0** - 现代用户模式驱动框架
2. **集成 FTDI VCP 驱动** - 作为过滤驱动工作在 FTDI 驱动之上
3. **支持 RS485 协议** - 实现完整的 RS485 通信协议栈
4. **兼容 Secure Boot** - 在企业环境中正常工作

## 🚨 **Secure Boot 问题解决**

### **问题**: 
```
The value is protected by Secure Boot policy and cannot be modified or deleted.
```

### **解决方案**: 
使用**自签名证书 + 受信任根证书**方法，无需禁用 Secure Boot 或启用测试签名。

## 🚀 **完整安装流程**

### **步骤 1: 创建驱动签名证书**
```batch
# 以管理员身份运行
.\CreateDriverCertificate.bat
```

这将：
- 创建自签名的代码签名证书
- 将证书安装到受信任的根证书存储
- 将证书安装到受信任的发布者存储
- 导出证书文件供验证

### **步骤 2: 构建并签名完整驱动**
```batch
# 以管理员身份运行
.\BuildSignedDriver.bat
```

这将：
- 编译 UMDF 驱动 DLL
- 创建驱动目录文件 (.cat)
- 使用自签名证书签名所有文件
- 构建包含签名文件的安装程序

### **步骤 3: 安装驱动**
```batch
.\FinalOutput\RS485DriverInstaller.exe
```

现在应该能够成功安装，无需测试签名模式。

## 🏗️ **UMDF 驱动架构**

### **驱动层次结构**:
```
用户应用程序
    ↓ (DeviceIoControl)
RS485FilterDriver.dll (UMDF 2.0 过滤驱动)
    ↓ (I/O 请求转发)
FTDI VCP 驱动 (内核模式)
    ↓
USB 硬件 (FTDI 芯片)
```

### **关键组件**:
- **RS485FilterDriver.dll** - 主要的 UMDF 驱动实现
- **RS485FilterDriver.inf** - 驱动安装信息文件
- **RS485FilterDriver.cat** - 驱动目录文件（包含签名信息）
- **RS485DriverInstaller.exe** - 集成安装程序

## 🔧 **技术实现细节**

### **UMDF 配置**:
```ini
[RS485Filter_Install.NT.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install_UmdfService
UmdfServiceOrder=RS485FilterDriver
UmdfKernelModeClientPolicy=AllowKernelModeClients
UmdfFileObjectPolicy=AllowNullAndUnknownFileObjects
UmdfHostProcessSharing=ProcessSharingDisabled

[RS485Filter_Install_UmdfService]
UmdfLibraryVersion=2.15.0
ServiceBinary=%13%\RS485FilterDriver.dll
UmdfDispatcher=FileHandle
UmdfImpersonationLevel=Impersonation
```

### **驱动功能**:
- **I/O 队列管理** - 处理来自应用程序的请求
- **缓冲区管理** - RS485 协议数据缓冲
- **协议处理** - RS485 帧格式化和解析
- **错误处理** - 完整的错误恢复机制

## 📋 **验证安装**

### **1. 检查驱动文件**:
```batch
dir C:\Windows\System32\RS485FilterDriver.dll
```

### **2. 检查设备管理器**:
1. 打开设备管理器
2. 连接 FTDI 设备
3. 查看"端口 (COM & LPT)"部分
4. 右键点击设备 → 属性 → 驱动程序选项卡
5. 应该显示 RS485 过滤驱动

### **3. 检查证书**:
```batch
certlm.msc
```
在"受信任的根证书颁发机构"中应该看到"RS485 Driver Certificate"

## 🎯 **为什么这个方案有效**

1. **自签名证书** - 创建有效的代码签名证书
2. **受信任根安装** - 将证书添加到系统受信任存储
3. **完整签名** - 签名所有驱动组件（DLL + CAT）
4. **UMDF 兼容性** - 使用正确的 UMDF 2.0 配置
5. **Secure Boot 兼容** - 不需要修改启动配置

## 🚨 **如果仍有问题**

### **备用方案 1: 企业证书**
如果您在企业环境中，可以：
1. 使用企业内部 CA 签发的证书
2. 通过组策略部署证书
3. 使用企业代码签名证书

### **备用方案 2: 硬件安全模块**
对于生产环境：
1. 使用 HSM 存储的证书
2. 通过 Azure Key Vault 等云服务
3. 使用 EV 代码签名证书

## 🎉 **预期结果**

完成上述步骤后，您将拥有：

✅ **完整的 UMDF 2.0 驱动** - 基于 WDK 构建  
✅ **FTDI 集成** - 作为过滤驱动工作  
✅ **Secure Boot 兼容** - 无需禁用安全功能  
✅ **企业级部署** - 适合生产环境  
✅ **完整的安装包** - 单一 EXE 安装程序  

这正是您要求的：**使用 WDK 和 UMDF 构建的基于 FTDI 驱动的完整 RS485 驱动解决方案**！
