@echo off
REM RS485 Driver Build Script for Visual Studio 2022
REM Fixed platform toolset issues, using standard VS2022 toolset
REM Fixed WDF header file path issues

echo ========================================
echo RS485 Driver Build Script (VS2022)
echo Fixed WDF path configuration issues
echo ========================================

REM Set Visual Studio 2022 environment
set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022
set MSBUILD_PATH=%VS_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe

REM Check Professional version, try Community if not found
if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe
)

REM Check Community version, try Enterprise if not found
if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe
)

if not exist "%MSBUILD_PATH%" (
    echo ERROR: Cannot find Visual Studio 2022 MSBuild
    echo Please ensure Visual Studio 2022 (Community/Professional/Enterprise) is installed
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo Found MSBuild: %MSBUILD_PATH%

REM Set build parameters
set SOLUTION_FILE=RS485Driver.sln
set CONFIGURATION=Debug
set PLATFORM=x64

echo Solution file: %SOLUTION_FILE%
echo Configuration: %CONFIGURATION%
echo Platform: %PLATFORM%

echo.
echo ========================================
echo Starting build...
echo ========================================

REM Clean previous build
echo Cleaning previous build...
"%MSBUILD_PATH%" "%SOLUTION_FILE%" /t:Clean /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal

REM Build solution
echo Building solution...
"%MSBUILD_PATH%" "%SOLUTION_FILE%" /t:Build /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:normal /m

if errorlevel 1 (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo Please check the following:
    echo 1. Ensure Visual Studio 2022 is installed
    echo 2. Ensure Windows 10/11 SDK is installed
    echo 3. Check if WDK path is correct
    echo 4. Review error messages above
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo ========================================
echo Build successful!
echo ========================================

REM Display output files
set OUTPUT_DIR=Build\%CONFIGURATION%\%PLATFORM%
echo Output directory: %OUTPUT_DIR%
echo.
echo Generated files:
if exist "%OUTPUT_DIR%\RS485FilterDriver.dll" echo   ✓ RS485FilterDriver.dll
if exist "%OUTPUT_DIR%\RS485FilterDriver.inf" echo   ✓ RS485FilterDriver.inf
if exist "%OUTPUT_DIR%\RS485DriverInterface.lib" echo   ✓ RS485DriverInterface.lib
if exist "%OUTPUT_DIR%\RS485Test.exe" echo   ✓ RS485Test.exe

echo.
echo ========================================
echo Next steps:
echo ========================================
echo 1. Enable test signing (requires admin privileges):
echo    bcdedit /set testsigning on
echo    Then restart computer
echo.
echo 2. Install driver:
echo    Right-click %OUTPUT_DIR%\RS485FilterDriver.inf
echo    Select "Install"
echo.
echo 3. Run test:
echo    %OUTPUT_DIR%\RS485Test.exe
echo.
echo Press any key to exit...
pause >nul
